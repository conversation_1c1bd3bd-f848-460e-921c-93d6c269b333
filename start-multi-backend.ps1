# 多端口后端启动脚本
# 支持在3000、3001、3002端口启动多个后端实例

param(
    [string]$Port = "3001",
    [switch]$All = $false
)

Write-Host "🚀 LoftChat 多端口后端启动脚本" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

if ($All) {
    Write-Host "启动所有端口的后端服务..." -ForegroundColor Yellow
    
    # 启动3000端口
    Write-Host "启动端口 3000..." -ForegroundColor Cyan
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; `$env:PORT=3000; npm run dev" -WindowStyle Normal
    Start-Sleep -Seconds 2
    
    # 启动3001端口
    Write-Host "启动端口 3001..." -ForegroundColor Cyan
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; `$env:PORT=3001; npm run dev" -WindowStyle Normal
    Start-Sleep -Seconds 2
    
    # 启动3002端口
    Write-Host "启动端口 3002..." -ForegroundColor Cyan
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; `$env:PORT=3002; npm run dev" -WindowStyle Normal
    
    Write-Host "✅ 所有后端服务启动完成！" -ForegroundColor Green
    Write-Host "📍 后端服务地址:" -ForegroundColor White
    Write-Host "   - http://localhost:3000" -ForegroundColor Blue
    Write-Host "   - http://localhost:3001" -ForegroundColor Blue
    Write-Host "   - http://localhost:3002" -ForegroundColor Blue
} else {
    Write-Host "启动端口 $Port 的后端服务..." -ForegroundColor Yellow
    
    # 设置环境变量并启动
    $env:PORT = $Port
    Set-Location backend
    
    Write-Host "📋 配置信息:" -ForegroundColor White
    Write-Host "   端口: $Port" -ForegroundColor Blue
    Write-Host "   目录: $(Get-Location)" -ForegroundColor Blue
    
    npm run dev
}

Write-Host ""
Write-Host "💡 使用说明:" -ForegroundColor Yellow
Write-Host "   启动单个端口: .\start-multi-backend.ps1 -Port 3001" -ForegroundColor White
Write-Host "   启动所有端口: .\start-multi-backend.ps1 -All" -ForegroundColor White
Write-Host ""
Write-Host "🔗 前端地址: http://localhost:5173 (或 5174, 5175)" -ForegroundColor Green
