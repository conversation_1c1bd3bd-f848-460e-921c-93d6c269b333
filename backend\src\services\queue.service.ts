import { Queue, Worker, Job } from 'bullmq';
import { redis } from '../config/redis';
import { UploadProviderService } from './upload-provider.service';
import { FileService } from './file.service';
import { prisma } from '../config/database';

export interface UploadJobData {
  imageId: number;
  userId: number;
  filePath: string;
  providerId?: number | undefined; // 用户指定的接口ID（可选）
  fileInfo: {
    originalName: string;
    mimeType: string;
    size: number;
    hash: string;
  };
}

export interface ImageProcessingJobData {
  imageId: number;
  filePath: string;
  operations: {
    generateThumbnails?: boolean;
    compress?: boolean;
    watermark?: boolean;
  };
}

export class QueueService {
  private static uploadQueue: Queue<UploadJobData>;
  private static imageProcessingQueue: Queue<ImageProcessingJobData>;
  private static uploadWorker: Worker<UploadJobData>;
  private static imageProcessingWorker: Worker<ImageProcessingJobData>;

  // 初始化队列系统
  static async initialize(): Promise<void> {
    try {
      // 创建队列
      QueueService.uploadQueue = new Queue<UploadJobData>('upload-queue', {
        connection: redis,
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      });

      QueueService.imageProcessingQueue = new Queue<ImageProcessingJobData>('image-processing-queue', {
        connection: redis,
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 25,
          attempts: 2,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        },
      });

      // 创建工作进程
      QueueService.uploadWorker = new Worker<UploadJobData>(
        'upload-queue',
        QueueService.processUploadJob,
        {
          connection: redis,
          concurrency: 5,
        }
      );

      QueueService.imageProcessingWorker = new Worker<ImageProcessingJobData>(
        'image-processing-queue',
        QueueService.processImageProcessingJob,
        {
          connection: redis,
          concurrency: 3,
        }
      );

      // 设置事件监听器
      QueueService.setupEventListeners();

      console.log('✅ 队列系统初始化成功');
    } catch (error) {
      console.error('❌ 队列系统初始化失败:', error);
      throw error;
    }
  }

  // 添加上传任务
  static async addUploadJob(data: UploadJobData): Promise<Job<UploadJobData>> {
    try {
      return await QueueService.uploadQueue.add('upload-file', data, {
        priority: 1,
        delay: 0,
      });
    } catch (error) {
      console.error('添加上传任务失败:', error);
      throw error;
    }
  }

  // 添加图片处理任务
  static async addImageProcessingJob(data: ImageProcessingJobData): Promise<Job<ImageProcessingJobData>> {
    try {
      return await QueueService.imageProcessingQueue.add('process-image', data, {
        priority: 2,
        delay: 1000, // 延迟1秒执行，确保上传完成
      });
    } catch (error) {
      console.error('添加图片处理任务失败:', error);
      throw error;
    }
  }

  // 处理上传任务
  private static async processUploadJob(job: Job<UploadJobData>): Promise<void> {
    const { imageId, userId, filePath, providerId, fileInfo } = job.data;

    try {
      console.log(`开始处理上传任务: 图片ID ${imageId}`);

      // 更新任务进度
      await job.updateProgress(10);

      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { userLevel: true }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      await job.updateProgress(20);

      // 获取上传接口
      let providers;

      if (providerId) {
        // 用户指定了接口，只使用该接口
        const provider = await UploadProviderService.getProviderById(providerId);
        if (!provider) {
          throw new Error('指定的接口不存在');
        }
        providers = [provider];
      } else {
        // 使用所有可用接口
        providers = await UploadProviderService.getAvailableProviders(userId, user.userLevel);
      }

      if (providers.length === 0) {
        throw new Error('没有可用的上传接口');
      }

      await job.updateProgress(30);

      // 读取文件
      const fileBuffer = await FileService.readFile(filePath);
      await job.updateProgress(40);

      // 上传到所有可用接口
      const uploadResults = await UploadProviderService.uploadToMultipleProviders(
        providers,
        fileBuffer,
        fileInfo.originalName,
        fileInfo.mimeType
      );

      await job.updateProgress(80);

      // 保存上传结果
      await UploadProviderService.saveUploadResults(imageId, uploadResults);
      await job.updateProgress(90);

      // 记录成功的上传
      const successfulUploads = uploadResults.filter(result => result.success);
      console.log(`图片 ${imageId} 上传完成: ${successfulUploads.length}/${uploadResults.length} 个接口成功`);

      // 添加图片处理任务
      await QueueService.addImageProcessingJob({
        imageId,
        filePath,
        operations: {
          generateThumbnails: true,
          compress: true,
          watermark: false,
        }
      });

      await job.updateProgress(100);

    } catch (error) {
      console.error(`上传任务失败 (图片ID: ${imageId}):`, error);
      
      // 更新图片状态为失败
      await prisma.image.update({
        where: { id: imageId },
        data: { uploadStatus: 'failed' }
      });

      throw error;
    }
  }

  // 处理图片处理任务
  private static async processImageProcessingJob(job: Job<ImageProcessingJobData>): Promise<void> {
    const { imageId, filePath, operations } = job.data;

    try {
      console.log(`开始处理图片处理任务: 图片ID ${imageId}`);

      await job.updateProgress(10);

      // 检查文件是否存在
      const fileExists = await FileService.fileExists(filePath);
      if (!fileExists) {
        throw new Error('源文件不存在');
      }

      const fileBuffer = await FileService.readFile(filePath);
      await job.updateProgress(30);

      // 生成缩略图
      if (operations.generateThumbnails) {
        await QueueService.generateThumbnails(imageId, fileBuffer);
        await job.updateProgress(60);
      }

      // 压缩图片
      if (operations.compress) {
        await QueueService.compressImage(imageId, fileBuffer);
        await job.updateProgress(80);
      }

      // 添加水印
      if (operations.watermark) {
        await QueueService.addWatermark(imageId, fileBuffer);
        await job.updateProgress(90);
      }

      console.log(`图片处理完成: 图片ID ${imageId}`);
      await job.updateProgress(100);

    } catch (error) {
      console.error(`图片处理任务失败 (图片ID: ${imageId}):`, error);
      throw error;
    }
  }

  // 生成缩略图
  private static async generateThumbnails(imageId: number, fileBuffer: Buffer): Promise<void> {
    try {
      const { ImageProcessingService } = await import('./image-processing.service');
      
      const thumbnailSizes = [
        { name: 'small', width: 150, height: 150 },
        { name: 'medium', width: 300, height: 300 },
        { name: 'large', width: 600, height: 600 },
      ];

      const thumbnails = await ImageProcessingService.generateMultipleThumbnails(fileBuffer, thumbnailSizes);

      // 保存缩略图文件
      for (const [size, buffer] of Object.entries(thumbnails)) {
        const thumbnailPath = `thumbnails/${imageId}_${size}.jpg`;
        await FileService.saveFile(buffer, thumbnailPath);
      }

      console.log(`缩略图生成完成: 图片ID ${imageId}`);
    } catch (error) {
      console.error(`生成缩略图失败 (图片ID: ${imageId}):`, error);
    }
  }

  // 压缩图片
  private static async compressImage(imageId: number, fileBuffer: Buffer): Promise<void> {
    try {
      const { ImageProcessingService } = await import('./image-processing.service');
      
      const optimized = await ImageProcessingService.optimizeImage(fileBuffer);
      
      if (optimized.compressionRatio > 10) { // 如果压缩率超过10%
        const compressedPath = `compressed/${imageId}_compressed.jpg`;
        await FileService.saveFile(optimized.buffer, compressedPath);
        
        console.log(`图片压缩完成: 图片ID ${imageId}, 压缩率: ${optimized.compressionRatio.toFixed(2)}%`);
      }
    } catch (error) {
      console.error(`压缩图片失败 (图片ID: ${imageId}):`, error);
    }
  }

  // 添加水印
  private static async addWatermark(imageId: number, fileBuffer: Buffer): Promise<void> {
    try {
      // 这里可以实现水印逻辑
      console.log(`水印添加完成: 图片ID ${imageId}`);
    } catch (error) {
      console.error(`添加水印失败 (图片ID: ${imageId}):`, error);
    }
  }

  // 设置事件监听器
  private static setupEventListeners(): void {
    // 上传队列事件
    QueueService.uploadWorker.on('completed', (job) => {
      console.log(`上传任务完成: ${job.id}`);
    });

    QueueService.uploadWorker.on('failed', (job, err) => {
      console.error(`上传任务失败: ${job?.id}`, err);
    });

    QueueService.uploadWorker.on('progress', (job, progress) => {
      console.log(`上传任务进度: ${job.id} - ${progress}%`);
    });

    // 图片处理队列事件
    QueueService.imageProcessingWorker.on('completed', (job) => {
      console.log(`图片处理任务完成: ${job.id}`);
    });

    QueueService.imageProcessingWorker.on('failed', (job, err) => {
      console.error(`图片处理任务失败: ${job?.id}`, err);
    });

    QueueService.imageProcessingWorker.on('progress', (job, progress) => {
      console.log(`图片处理任务进度: ${job.id} - ${progress}%`);
    });
  }

  // 获取队列状态
  static async getQueueStats(): Promise<{
    upload: any;
    imageProcessing: any;
  }> {
    try {
      const uploadStats = await QueueService.uploadQueue.getJobCounts();
      const imageProcessingStats = await QueueService.imageProcessingQueue.getJobCounts();

      return {
        upload: uploadStats,
        imageProcessing: imageProcessingStats,
      };
    } catch (error) {
      console.error('获取队列状态失败:', error);
      return {
        upload: {},
        imageProcessing: {},
      };
    }
  }

  // 清理队列
  static async cleanQueues(): Promise<void> {
    try {
      await QueueService.uploadQueue.clean(24 * 60 * 60 * 1000, 100); // 清理24小时前的任务
      await QueueService.imageProcessingQueue.clean(24 * 60 * 60 * 1000, 100);
      console.log('队列清理完成');
    } catch (error) {
      console.error('清理队列失败:', error);
    }
  }

  // 关闭队列系统
  static async shutdown(): Promise<void> {
    try {
      await QueueService.uploadWorker.close();
      await QueueService.imageProcessingWorker.close();
      await QueueService.uploadQueue.close();
      await QueueService.imageProcessingQueue.close();
      console.log('队列系统已关闭');
    } catch (error) {
      console.error('关闭队列系统失败:', error);
    }
  }
}
