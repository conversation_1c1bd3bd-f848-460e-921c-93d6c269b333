"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const database_1 = require("../config/database");
const client_1 = require("@prisma/client");
class AnalyticsService {
    async getAnalyticsOverview(timeRange = 'month') {
        const now = new Date();
        const startDate = this.getStartDate(now, timeRange);
        const [userStats, uploadStats, accessStats, storageStats, trendData] = await Promise.all([
            this.getUserStats(startDate, now),
            this.getUploadStats(startDate, now),
            this.getAccessStats(startDate, now),
            this.getStorageStats(),
            this.getTrendData(startDate, now, timeRange)
        ]);
        return {
            timeRange,
            period: {
                start: startDate,
                end: now
            },
            summary: {
                users: userStats,
                uploads: uploadStats,
                access: accessStats,
                storage: storageStats
            },
            trends: trendData
        };
    }
    async getUserAnalytics(timeRange = 'month') {
        const now = new Date();
        const startDate = this.getStartDate(now, timeRange);
        const [userLevelDistribution, userActivityTrends, userGeoDistribution, userDeviceStats, activeUserStats] = await Promise.all([
            this.getUserLevelDistribution(),
            this.getUserActivityTrends(startDate, now),
            this.getUserGeoDistribution(startDate, now),
            this.getUserDeviceStats(startDate, now),
            this.getActiveUserStats(startDate, now)
        ]);
        return {
            timeRange,
            period: { start: startDate, end: now },
            levelDistribution: userLevelDistribution,
            activityTrends: userActivityTrends,
            geoDistribution: userGeoDistribution,
            deviceStats: userDeviceStats,
            activeUsers: activeUserStats
        };
    }
    async getUploadAnalytics(timeRange = 'month') {
        const now = new Date();
        const startDate = this.getStartDate(now, timeRange);
        const [uploadTrends, fileTypeDistribution, fileSizeDistribution, topUploaders, uploadSuccessRate, popularImages] = await Promise.all([
            this.getUploadTrends(startDate, now),
            this.getFileTypeDistribution(startDate, now),
            this.getFileSizeDistribution(startDate, now),
            this.getTopUploaders(startDate, now),
            this.getUploadSuccessRate(startDate, now),
            this.getPopularImages(startDate, now)
        ]);
        return {
            timeRange,
            period: { start: startDate, end: now },
            trends: uploadTrends,
            fileTypes: fileTypeDistribution,
            fileSizes: fileSizeDistribution,
            topUploaders,
            successRate: uploadSuccessRate,
            popularImages
        };
    }
    async getUserStats(startDate, endDate) {
        const [totalUsers, newUsers, activeUsers, bannedUsers] = await Promise.all([
            database_1.prisma.user.count(),
            database_1.prisma.user.count({
                where: { createdAt: { gte: startDate, lte: endDate } }
            }),
            database_1.prisma.user.count({
                where: {
                    status: 'active',
                    lastActiveAt: { gte: startDate, lte: endDate }
                }
            }),
            database_1.prisma.user.count({
                where: { status: 'banned' }
            })
        ]);
        return {
            total: totalUsers,
            new: newUsers,
            active: activeUsers,
            banned: bannedUsers
        };
    }
    async getUploadStats(startDate, endDate) {
        const [totalUploads, newUploads, successfulUploads, failedUploads] = await Promise.all([
            database_1.prisma.userImage.count(),
            database_1.prisma.userImage.count({
                where: { createdAt: { gte: startDate, lte: endDate } }
            }),
            database_1.prisma.uploadLog.count({
                where: {
                    createdAt: { gte: startDate, lte: endDate },
                    isSuccess: true
                }
            }),
            database_1.prisma.uploadLog.count({
                where: {
                    createdAt: { gte: startDate, lte: endDate },
                    isSuccess: false
                }
            })
        ]);
        return {
            total: totalUploads,
            new: newUploads,
            successful: successfulUploads,
            failed: failedUploads
        };
    }
    async getAccessStats(startDate, endDate) {
        const [totalAccess, uniqueVisitors, totalPageViews] = await Promise.all([
            database_1.prisma.accessLog.count({
                where: { createdAt: { gte: startDate, lte: endDate } }
            }),
            database_1.prisma.accessLog.groupBy({
                by: ['ipAddress'],
                where: { createdAt: { gte: startDate, lte: endDate } },
                _count: true
            }).then(result => result.length),
            database_1.prisma.userActivityLog.count({
                where: {
                    createdAt: { gte: startDate, lte: endDate },
                    activityType: { in: ['page_view', 'dashboard_view', 'settings_view'] }
                }
            })
        ]);
        return {
            total: totalAccess,
            uniqueVisitors,
            pageViews: totalPageViews
        };
    }
    async getStorageStats() {
        const result = await database_1.prisma.image.aggregate({
            _sum: { fileSize: true },
            _count: true,
            _avg: { fileSize: true },
            where: { isDeleted: false }
        });
        return {
            totalSize: Number(result._sum.fileSize || 0),
            totalFiles: result._count,
            averageSize: Number(result._avg.fileSize || 0)
        };
    }
    async getUserLevelDistribution() {
        const distribution = await database_1.prisma.user.groupBy({
            by: ['userLevel'],
            _count: true,
            orderBy: { _count: { userLevel: 'desc' } }
        });
        const total = distribution.reduce((sum, item) => sum + item._count, 0);
        return distribution.map(item => ({
            level: item.userLevel,
            count: item._count,
            percentage: total > 0 ? Math.round((item._count / total) * 100 * 100) / 100 : 0
        }));
    }
    async getUserActivityTrends(startDate, endDate) {
        const activities = await database_1.prisma.userActivityLog.groupBy({
            by: ['activityType'],
            where: { createdAt: { gte: startDate, lte: endDate } },
            _count: true,
            orderBy: { _count: { activityType: 'desc' } }
        });
        return activities.map(item => ({
            type: item.activityType,
            count: item._count
        }));
    }
    async getUserGeoDistribution(startDate, endDate) {
        const geoData = await database_1.prisma.userActivityLog.groupBy({
            by: ['location'],
            where: {
                createdAt: { gte: startDate, lte: endDate },
                location: { not: null }
            },
            _count: true,
            orderBy: { _count: { location: 'desc' } }
        });
        return geoData.map(item => ({
            location: item.location,
            count: item._count
        }));
    }
    async getUserDeviceStats(startDate, endDate) {
        const deviceLogs = await database_1.prisma.userActivityLog.findMany({
            where: {
                createdAt: { gte: startDate, lte: endDate },
                deviceInfo: { not: client_1.Prisma.JsonNull }
            },
            select: { deviceInfo: true }
        });
        const deviceStats = {
            mobile: 0,
            desktop: 0,
            browsers: {}
        };
        deviceLogs.forEach(log => {
            if (log.deviceInfo && typeof log.deviceInfo === 'object') {
                const device = log.deviceInfo;
                if (device.isMobile) {
                    deviceStats.mobile++;
                }
                else {
                    deviceStats.desktop++;
                }
                if (device.browser) {
                    const browser = device.browser.split('/')[0];
                    deviceStats.browsers[browser] = (deviceStats.browsers[browser] || 0) + 1;
                }
            }
        });
        return deviceStats;
    }
    async getActiveUserStats(startDate, endDate) {
        const dailyActive = await database_1.prisma.user.count({
            where: {
                lastActiveAt: {
                    gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
                }
            }
        });
        const weeklyActive = await database_1.prisma.user.count({
            where: {
                lastActiveAt: {
                    gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                }
            }
        });
        const monthlyActive = await database_1.prisma.user.count({
            where: {
                lastActiveAt: {
                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                }
            }
        });
        return {
            daily: dailyActive,
            weekly: weeklyActive,
            monthly: monthlyActive
        };
    }
    getStartDate(now, timeRange) {
        const date = new Date(now);
        switch (timeRange) {
            case 'day':
                date.setHours(0, 0, 0, 0);
                break;
            case 'week':
                date.setDate(date.getDate() - 7);
                break;
            case 'month':
                date.setMonth(date.getMonth() - 1);
                break;
            case 'year':
                date.setFullYear(date.getFullYear() - 1);
                break;
            default:
                date.setMonth(date.getMonth() - 1);
        }
        return date;
    }
    async getUploadTrends(startDate, endDate) {
        const userImageCount = await database_1.prisma.userImage.count();
        if (userImageCount === 0) {
            return [];
        }
        const trends = await database_1.prisma.$queryRaw `
      SELECT
        DATE(created_at) as date,
        COUNT(*) as count
      FROM user_images
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `;
        return trends.map(item => ({
            date: item.date,
            count: Number(item.count)
        }));
    }
    async getFileTypeDistribution(startDate, endDate) {
        const distribution = await database_1.prisma.image.groupBy({
            by: ['mimeType'],
            where: {
                createdAt: { gte: startDate, lte: endDate },
                isDeleted: false
            },
            _count: true,
            orderBy: { _count: { mimeType: 'desc' } }
        });
        return distribution.map(item => ({
            type: item.mimeType,
            count: item._count
        }));
    }
    async getFileSizeDistribution(startDate, endDate) {
        const images = await database_1.prisma.image.findMany({
            where: {
                createdAt: { gte: startDate, lte: endDate },
                isDeleted: false
            },
            select: { fileSize: true }
        });
        const sizeRanges = {
            'small': 0,
            'medium': 0,
            'large': 0,
            'xlarge': 0
        };
        images.forEach(image => {
            const sizeInMB = Number(image.fileSize) / (1024 * 1024);
            if (sizeInMB < 1) {
                sizeRanges.small++;
            }
            else if (sizeInMB < 5) {
                sizeRanges.medium++;
            }
            else if (sizeInMB < 10) {
                sizeRanges.large++;
            }
            else {
                sizeRanges.xlarge++;
            }
        });
        return Object.entries(sizeRanges).map(([range, count]) => ({
            range,
            count
        }));
    }
    async getTopUploaders(startDate, endDate, limit = 10) {
        const topUploaders = await database_1.prisma.userImage.groupBy({
            by: ['userId'],
            where: { createdAt: { gte: startDate, lte: endDate } },
            _count: true,
            orderBy: { _count: { userId: 'desc' } },
            take: limit
        });
        const userIds = topUploaders.map(item => item.userId);
        const users = await database_1.prisma.user.findMany({
            where: { id: { in: userIds } },
            select: { id: true, username: true, userLevel: true }
        });
        return topUploaders.map(item => {
            const user = users.find(u => u.id === item.userId);
            return {
                userId: item.userId,
                username: user?.username || 'Unknown',
                userLevel: user?.userLevel || 'free',
                uploadCount: item._count
            };
        });
    }
    async getUploadSuccessRate(startDate, endDate) {
        const [successful, failed] = await Promise.all([
            database_1.prisma.uploadLog.count({
                where: {
                    createdAt: { gte: startDate, lte: endDate },
                    isSuccess: true
                }
            }),
            database_1.prisma.uploadLog.count({
                where: {
                    createdAt: { gte: startDate, lte: endDate },
                    isSuccess: false
                }
            })
        ]);
        const total = successful + failed;
        const successRate = total > 0 ? (successful / total) * 100 : 0;
        return {
            successful,
            failed,
            total,
            successRate: Math.round(successRate * 100) / 100
        };
    }
    async getPopularImages(startDate, endDate, limit = 10) {
        const userImageCount = await database_1.prisma.userImage.count();
        if (userImageCount === 0) {
            return [];
        }
        const popularImages = await database_1.prisma.userImage.findMany({
            where: { createdAt: { gte: startDate, lte: endDate } },
            include: {
                image: {
                    select: {
                        id: true,
                        originalName: true,
                        systemUrl: true,
                        fileSize: true,
                        mimeType: true,
                        accessLogs: {
                            select: { id: true }
                        }
                    }
                },
                user: {
                    select: { username: true, userLevel: true }
                }
            },
            orderBy: { accessCount: 'desc' },
            take: limit
        });
        return popularImages.map(item => ({
            imageId: item.image.id,
            fileName: item.image.originalName,
            fileSize: Number(item.image.fileSize),
            mimeType: item.image.mimeType,
            systemUrl: item.image.systemUrl,
            accessCount: item.accessCount,
            uploader: {
                username: item.user.username,
                level: item.user.userLevel
            },
            totalAccess: item.image.accessLogs.length
        }));
    }
    async getTrendData(startDate, endDate, timeRange) {
        const dateFormat = this.getDateFormat(timeRange);
        const uploadTrends = await database_1.prisma.$queryRaw `
      SELECT
        ${client_1.Prisma.raw(dateFormat)} as date,
        COUNT(*) as count
      FROM user_images
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY ${client_1.Prisma.raw(dateFormat)}
      ORDER BY date ASC
    `;
        const userTrends = await database_1.prisma.$queryRaw `
      SELECT
        ${client_1.Prisma.raw(dateFormat)} as date,
        COUNT(*) as count
      FROM users
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY ${client_1.Prisma.raw(dateFormat)}
      ORDER BY date ASC
    `;
        const accessTrends = await database_1.prisma.$queryRaw `
      SELECT
        ${client_1.Prisma.raw(dateFormat)} as date,
        COUNT(*) as count
      FROM access_logs
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY ${client_1.Prisma.raw(dateFormat)}
      ORDER BY date ASC
    `;
        return {
            uploads: uploadTrends.map(item => ({ date: item.date, count: Number(item.count) })),
            users: userTrends.map(item => ({ date: item.date, count: Number(item.count) })),
            access: accessTrends.map(item => ({ date: item.date, count: Number(item.count) }))
        };
    }
    getDateFormat(timeRange) {
        switch (timeRange) {
            case 'day':
                return "TO_CHAR(created_at, 'YYYY-MM-DD HH24:00:00')";
            case 'week':
            case 'month':
                return "TO_CHAR(created_at, 'YYYY-MM-DD')";
            case 'year':
                return "TO_CHAR(created_at, 'YYYY-MM')";
            default:
                return "TO_CHAR(created_at, 'YYYY-MM-DD')";
        }
    }
    async getRealtimeStats() {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        const [todayUploads, yesterdayUploads, onlineUsers, todayNewUsers, systemLoad] = await Promise.all([
            database_1.prisma.userImage.count({ where: { createdAt: { gte: today } } }),
            database_1.prisma.userImage.count({ where: { createdAt: { gte: yesterday, lt: today } } }),
            this.getOnlineUsersCount(),
            database_1.prisma.user.count({ where: { createdAt: { gte: today } } }),
            this.getSystemLoadStats()
        ]);
        const uploadGrowth = yesterdayUploads > 0
            ? ((todayUploads - yesterdayUploads) / yesterdayUploads) * 100
            : 0;
        return {
            uploads: {
                today: todayUploads,
                yesterday: yesterdayUploads,
                growth: Math.round(uploadGrowth * 100) / 100
            },
            users: {
                online: onlineUsers,
                newToday: todayNewUsers
            },
            system: systemLoad
        };
    }
    async getOnlineUsersCount() {
        const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
        return await database_1.prisma.user.count({
            where: {
                lastActiveAt: { gte: fifteenMinutesAgo },
                status: 'active'
            }
        });
    }
    async getSystemLoadStats() {
        const recentMetrics = await database_1.prisma.systemMetric.findMany({
            where: {
                createdAt: { gte: new Date(Date.now() - 5 * 60 * 1000) }
            },
            orderBy: { createdAt: 'desc' },
            take: 10
        });
        if (recentMetrics.length === 0) {
            return { cpu: 0, memory: 0, disk: 0 };
        }
        const cpuMetrics = recentMetrics.filter(m => m.metricType === 'cpu_usage');
        const memoryMetrics = recentMetrics.filter(m => m.metricType === 'memory_usage');
        const diskMetrics = recentMetrics.filter(m => m.metricType === 'disk_usage');
        const avgCpu = cpuMetrics.length > 0
            ? cpuMetrics.reduce((sum, m) => sum + Number(m.metricValue), 0) / cpuMetrics.length
            : 0;
        const avgMemory = memoryMetrics.length > 0
            ? memoryMetrics.reduce((sum, m) => sum + Number(m.metricValue), 0) / memoryMetrics.length
            : 0;
        const avgDisk = diskMetrics.length > 0
            ? diskMetrics.reduce((sum, m) => sum + Number(m.metricValue), 0) / diskMetrics.length
            : 0;
        return {
            cpu: Math.round(avgCpu * 100) / 100,
            memory: Math.round(avgMemory * 100) / 100,
            disk: Math.round(avgDisk * 100) / 100
        };
    }
}
exports.AnalyticsService = AnalyticsService;
//# sourceMappingURL=analytics.service.js.map