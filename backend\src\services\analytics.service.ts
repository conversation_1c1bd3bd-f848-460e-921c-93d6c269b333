import { prisma } from '../config/database';
import { Prisma } from '@prisma/client';

/**
 * 数据分析服务类
 * 提供各种数据统计和分析功能
 */
export class AnalyticsService {
  
  /**
   * 获取数据分析概览
   */
  async getAnalyticsOverview(timeRange: 'day' | 'week' | 'month' | 'year' = 'month') {
    const now = new Date();
    const startDate = this.getStartDate(now, timeRange);
    
    // 并行获取各项统计数据
    const [
      userStats,
      uploadStats,
      accessStats,
      storageStats,
      trendData
    ] = await Promise.all([
      this.getUserStats(startDate, now),
      this.getUploadStats(startDate, now),
      this.getAccessStats(startDate, now),
      this.getStorageStats(),
      this.getTrendData(startDate, now, timeRange)
    ]);

    return {
      timeRange,
      period: {
        start: startDate,
        end: now
      },
      summary: {
        users: userStats,
        uploads: uploadStats,
        access: accessStats,
        storage: storageStats
      },
      trends: trendData
    };
  }

  /**
   * 获取用户行为分析数据
   */
  async getUserAnalytics(timeRange: 'day' | 'week' | 'month' | 'year' = 'month') {
    const now = new Date();
    const startDate = this.getStartDate(now, timeRange);

    const [
      userLevelDistribution,
      userActivityTrends,
      userGeoDistribution,
      userDeviceStats,
      activeUserStats
    ] = await Promise.all([
      this.getUserLevelDistribution(),
      this.getUserActivityTrends(startDate, now),
      this.getUserGeoDistribution(startDate, now),
      this.getUserDeviceStats(startDate, now),
      this.getActiveUserStats(startDate, now)
    ]);

    return {
      timeRange,
      period: { start: startDate, end: now },
      levelDistribution: userLevelDistribution,
      activityTrends: userActivityTrends,
      geoDistribution: userGeoDistribution,
      deviceStats: userDeviceStats,
      activeUsers: activeUserStats
    };
  }

  /**
   * 获取上传数据分析
   */
  async getUploadAnalytics(timeRange: 'day' | 'week' | 'month' | 'year' = 'month') {
    const now = new Date();
    const startDate = this.getStartDate(now, timeRange);

    const [
      uploadTrends,
      fileTypeDistribution,
      fileSizeDistribution,
      topUploaders,
      uploadSuccessRate,
      popularImages
    ] = await Promise.all([
      this.getUploadTrends(startDate, now),
      this.getFileTypeDistribution(startDate, now),
      this.getFileSizeDistribution(startDate, now),
      this.getTopUploaders(startDate, now),
      this.getUploadSuccessRate(startDate, now),
      this.getPopularImages(startDate, now)
    ]);

    return {
      timeRange,
      period: { start: startDate, end: now },
      trends: uploadTrends,
      fileTypes: fileTypeDistribution,
      fileSizes: fileSizeDistribution,
      topUploaders,
      successRate: uploadSuccessRate,
      popularImages
    };
  }

  /**
   * 获取用户统计数据
   */
  private async getUserStats(startDate: Date, endDate: Date) {
    const [totalUsers, newUsers, activeUsers, bannedUsers] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({
        where: { createdAt: { gte: startDate, lte: endDate } }
      }),
      prisma.user.count({
        where: { 
          status: 'active',
          lastActiveAt: { gte: startDate, lte: endDate }
        }
      }),
      prisma.user.count({
        where: { status: 'banned' }
      })
    ]);

    return {
      total: totalUsers,
      new: newUsers,
      active: activeUsers,
      banned: bannedUsers
    };
  }

  /**
   * 获取上传统计数据
   */
  private async getUploadStats(startDate: Date, endDate: Date) {
    const [totalUploads, newUploads, successfulUploads, failedUploads] = await Promise.all([
      prisma.userImage.count(),
      prisma.userImage.count({
        where: { createdAt: { gte: startDate, lte: endDate } }
      }),
      prisma.uploadLog.count({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          isSuccess: true
        }
      }),
      prisma.uploadLog.count({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          isSuccess: false
        }
      })
    ]);

    return {
      total: totalUploads,
      new: newUploads,
      successful: successfulUploads,
      failed: failedUploads
    };
  }

  /**
   * 获取访问统计数据
   */
  private async getAccessStats(startDate: Date, endDate: Date) {
    const [totalAccess, uniqueVisitors, totalPageViews] = await Promise.all([
      prisma.accessLog.count({
        where: { createdAt: { gte: startDate, lte: endDate } }
      }),
      prisma.accessLog.groupBy({
        by: ['ipAddress'],
        where: { createdAt: { gte: startDate, lte: endDate } },
        _count: true
      }).then(result => result.length),
      prisma.userActivityLog.count({
        where: { 
          createdAt: { gte: startDate, lte: endDate },
          activityType: { in: ['page_view', 'dashboard_view', 'settings_view'] }
        }
      })
    ]);

    return {
      total: totalAccess,
      uniqueVisitors,
      pageViews: totalPageViews
    };
  }

  /**
   * 获取存储统计数据
   */
  private async getStorageStats() {
    const result = await prisma.image.aggregate({
      _sum: { fileSize: true },
      _count: true,
      _avg: { fileSize: true },
      where: { isDeleted: false }
    });

    return {
      totalSize: Number(result._sum.fileSize || 0),
      totalFiles: result._count,
      averageSize: Number(result._avg.fileSize || 0)
    };
  }

  /**
   * 获取用户等级分布
   */
  private async getUserLevelDistribution() {
    const distribution = await prisma.user.groupBy({
      by: ['userLevel'],
      _count: true,
      orderBy: { _count: { userLevel: 'desc' } }
    });

    const total = distribution.reduce((sum, item) => sum + item._count, 0);

    return distribution.map(item => ({
      level: item.userLevel,
      count: item._count,
      percentage: total > 0 ? Math.round((item._count / total) * 100 * 100) / 100 : 0
    }));
  }

  /**
   * 获取用户活动趋势
   */
  private async getUserActivityTrends(startDate: Date, endDate: Date) {
    const activities = await prisma.userActivityLog.groupBy({
      by: ['activityType'],
      where: { createdAt: { gte: startDate, lte: endDate } },
      _count: true,
      orderBy: { _count: { activityType: 'desc' } }
    });

    return activities.map(item => ({
      type: item.activityType,
      count: item._count
    }));
  }

  /**
   * 获取用户地理分布
   */
  private async getUserGeoDistribution(startDate: Date, endDate: Date) {
    const geoData = await prisma.userActivityLog.groupBy({
      by: ['location'],
      where: { 
        createdAt: { gte: startDate, lte: endDate },
        location: { not: null }
      },
      _count: true,
      orderBy: { _count: { location: 'desc' } }
    });

    return geoData.map(item => ({
      location: item.location,
      count: item._count
    }));
  }

  /**
   * 获取用户设备统计
   */
  private async getUserDeviceStats(startDate: Date, endDate: Date) {
    // 这里需要解析 deviceInfo JSON 字段
    const deviceLogs = await prisma.userActivityLog.findMany({
      where: {
        createdAt: { gte: startDate, lte: endDate },
        deviceInfo: { not: Prisma.JsonNull }
      },
      select: { deviceInfo: true }
    });

    const deviceStats = {
      mobile: 0,
      desktop: 0,
      browsers: {} as Record<string, number>
    };

    deviceLogs.forEach(log => {
      if (log.deviceInfo && typeof log.deviceInfo === 'object') {
        const device = log.deviceInfo as any;
        if (device.isMobile) {
          deviceStats.mobile++;
        } else {
          deviceStats.desktop++;
        }
        
        if (device.browser) {
          const browser = device.browser.split('/')[0]; // 提取浏览器名称
          deviceStats.browsers[browser] = (deviceStats.browsers[browser] || 0) + 1;
        }
      }
    });

    return deviceStats;
  }

  /**
   * 获取活跃用户统计
   */
  private async getActiveUserStats(startDate: Date, endDate: Date) {
    const dailyActive = await prisma.user.count({
      where: {
        lastActiveAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
        }
      }
    });

    const weeklyActive = await prisma.user.count({
      where: {
        lastActiveAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
        }
      }
    });

    const monthlyActive = await prisma.user.count({
      where: {
        lastActiveAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 最近30天
        }
      }
    });

    return {
      daily: dailyActive,
      weekly: weeklyActive,
      monthly: monthlyActive
    };
  }

  /**
   * 根据时间范围获取开始日期
   */
  private getStartDate(now: Date, timeRange: string): Date {
    const date = new Date(now);
    
    switch (timeRange) {
      case 'day':
        date.setHours(0, 0, 0, 0);
        break;
      case 'week':
        date.setDate(date.getDate() - 7);
        break;
      case 'month':
        date.setMonth(date.getMonth() - 1);
        break;
      case 'year':
        date.setFullYear(date.getFullYear() - 1);
        break;
      default:
        date.setMonth(date.getMonth() - 1);
    }
    
    return date;
  }



  /**
   * 获取上传趋势数据
   */
  private async getUploadTrends(startDate: Date, endDate: Date) {
    // 检查是否有用户图片数据
    const userImageCount = await prisma.userImage.count();
    if (userImageCount === 0) {
      return [];
    }

    const trends = await prisma.$queryRaw<Array<{date: string, count: bigint}>>`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as count
      FROM user_images
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `;

    return trends.map(item => ({
      date: item.date,
      count: Number(item.count)
    }));
  }

  /**
   * 获取文件类型分布
   */
  private async getFileTypeDistribution(startDate: Date, endDate: Date) {
    const distribution = await prisma.image.groupBy({
      by: ['mimeType'],
      where: {
        createdAt: { gte: startDate, lte: endDate },
        isDeleted: false
      },
      _count: true,
      orderBy: { _count: { mimeType: 'desc' } }
    });

    return distribution.map(item => ({
      type: item.mimeType,
      count: item._count
    }));
  }

  /**
   * 获取文件大小分布
   */
  private async getFileSizeDistribution(startDate: Date, endDate: Date) {
    const images = await prisma.image.findMany({
      where: {
        createdAt: { gte: startDate, lte: endDate },
        isDeleted: false
      },
      select: { fileSize: true }
    });

    const sizeRanges = {
      'small': 0,    // < 1MB
      'medium': 0,   // 1MB - 5MB
      'large': 0,    // 5MB - 10MB
      'xlarge': 0    // > 10MB
    };

    images.forEach(image => {
      const sizeInMB = Number(image.fileSize) / (1024 * 1024);
      if (sizeInMB < 1) {
        sizeRanges.small++;
      } else if (sizeInMB < 5) {
        sizeRanges.medium++;
      } else if (sizeInMB < 10) {
        sizeRanges.large++;
      } else {
        sizeRanges.xlarge++;
      }
    });

    return Object.entries(sizeRanges).map(([range, count]) => ({
      range,
      count
    }));
  }

  /**
   * 获取上传量最多的用户
   */
  private async getTopUploaders(startDate: Date, endDate: Date, limit: number = 10) {
    const topUploaders = await prisma.userImage.groupBy({
      by: ['userId'],
      where: { createdAt: { gte: startDate, lte: endDate } },
      _count: true,
      orderBy: { _count: { userId: 'desc' } },
      take: limit
    });

    // 获取用户信息
    const userIds = topUploaders.map(item => item.userId);
    const users = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: { id: true, username: true, userLevel: true }
    });

    return topUploaders.map(item => {
      const user = users.find(u => u.id === item.userId);
      return {
        userId: item.userId,
        username: user?.username || 'Unknown',
        userLevel: user?.userLevel || 'free',
        uploadCount: item._count
      };
    });
  }

  /**
   * 获取上传成功率
   */
  private async getUploadSuccessRate(startDate: Date, endDate: Date) {
    const [successful, failed] = await Promise.all([
      prisma.uploadLog.count({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          isSuccess: true
        }
      }),
      prisma.uploadLog.count({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          isSuccess: false
        }
      })
    ]);

    const total = successful + failed;
    const successRate = total > 0 ? (successful / total) * 100 : 0;

    return {
      successful,
      failed,
      total,
      successRate: Math.round(successRate * 100) / 100
    };
  }

  /**
   * 获取热门图片
   */
  private async getPopularImages(startDate: Date, endDate: Date, limit: number = 10) {
    // 如果没有用户图片关联数据，返回空数组
    const userImageCount = await prisma.userImage.count();
    if (userImageCount === 0) {
      return [];
    }

    const popularImages = await prisma.userImage.findMany({
      where: { createdAt: { gte: startDate, lte: endDate } },
      include: {
        image: {
          select: {
            id: true,
            originalName: true,
            systemUrl: true,
            fileSize: true,
            mimeType: true,
            accessLogs: {
              select: { id: true }
            }
          }
        },
        user: {
          select: { username: true, userLevel: true }
        }
      },
      orderBy: { accessCount: 'desc' },
      take: limit
    });

    return popularImages.map(item => ({
      imageId: item.image.id,
      fileName: item.image.originalName,
      fileSize: Number(item.image.fileSize),
      mimeType: item.image.mimeType,
      systemUrl: item.image.systemUrl,
      accessCount: item.accessCount,
      uploader: {
        username: item.user.username,
        level: item.user.userLevel
      },
      totalAccess: item.image.accessLogs.length
    }));
  }

  /**
   * 获取详细的趋势数据
   */
  private async getTrendData(startDate: Date, endDate: Date, timeRange: string) {
    const dateFormat = this.getDateFormat(timeRange);

    // 上传趋势
    const uploadTrends = await prisma.$queryRaw<Array<{date: string, count: bigint}>>`
      SELECT
        ${Prisma.raw(dateFormat)} as date,
        COUNT(*) as count
      FROM user_images
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY ${Prisma.raw(dateFormat)}
      ORDER BY date ASC
    `;

    // 用户注册趋势
    const userTrends = await prisma.$queryRaw<Array<{date: string, count: bigint}>>`
      SELECT
        ${Prisma.raw(dateFormat)} as date,
        COUNT(*) as count
      FROM users
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY ${Prisma.raw(dateFormat)}
      ORDER BY date ASC
    `;

    // 访问趋势
    const accessTrends = await prisma.$queryRaw<Array<{date: string, count: bigint}>>`
      SELECT
        ${Prisma.raw(dateFormat)} as date,
        COUNT(*) as count
      FROM access_logs
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY ${Prisma.raw(dateFormat)}
      ORDER BY date ASC
    `;

    return {
      uploads: uploadTrends.map(item => ({ date: item.date, count: Number(item.count) })),
      users: userTrends.map(item => ({ date: item.date, count: Number(item.count) })),
      access: accessTrends.map(item => ({ date: item.date, count: Number(item.count) }))
    };
  }

  /**
   * 根据时间范围获取日期格式化字符串
   */
  private getDateFormat(timeRange: string): string {
    switch (timeRange) {
      case 'day':
        return "TO_CHAR(created_at, 'YYYY-MM-DD HH24:00:00')";
      case 'week':
      case 'month':
        return "TO_CHAR(created_at, 'YYYY-MM-DD')";
      case 'year':
        return "TO_CHAR(created_at, 'YYYY-MM')";
      default:
        return "TO_CHAR(created_at, 'YYYY-MM-DD')";
    }
  }

  /**
   * 获取实时统计数据（用于仪表板）
   */
  async getRealtimeStats() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

    const [
      todayUploads,
      yesterdayUploads,
      onlineUsers,
      todayNewUsers,
      systemLoad
    ] = await Promise.all([
      prisma.userImage.count({ where: { createdAt: { gte: today } } }),
      prisma.userImage.count({ where: { createdAt: { gte: yesterday, lt: today } } }),
      this.getOnlineUsersCount(),
      prisma.user.count({ where: { createdAt: { gte: today } } }),
      this.getSystemLoadStats()
    ]);

    const uploadGrowth = yesterdayUploads > 0
      ? ((todayUploads - yesterdayUploads) / yesterdayUploads) * 100
      : 0;

    return {
      uploads: {
        today: todayUploads,
        yesterday: yesterdayUploads,
        growth: Math.round(uploadGrowth * 100) / 100
      },
      users: {
        online: onlineUsers,
        newToday: todayNewUsers
      },
      system: systemLoad
    };
  }

  /**
   * 获取在线用户数（基于最近活动时间）
   */
  private async getOnlineUsersCount(): Promise<number> {
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    return await prisma.user.count({
      where: {
        lastActiveAt: { gte: fifteenMinutesAgo },
        status: 'active'
      }
    });
  }

  /**
   * 获取系统负载统计
   */
  private async getSystemLoadStats() {
    // 获取最近的系统指标
    const recentMetrics = await prisma.systemMetric.findMany({
      where: {
        createdAt: { gte: new Date(Date.now() - 5 * 60 * 1000) } // 最近5分钟
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    if (recentMetrics.length === 0) {
      return { cpu: 0, memory: 0, disk: 0 };
    }

    const cpuMetrics = recentMetrics.filter(m => m.metricType === 'cpu_usage');
    const memoryMetrics = recentMetrics.filter(m => m.metricType === 'memory_usage');
    const diskMetrics = recentMetrics.filter(m => m.metricType === 'disk_usage');

    const avgCpu = cpuMetrics.length > 0
      ? cpuMetrics.reduce((sum, m) => sum + Number(m.metricValue), 0) / cpuMetrics.length
      : 0;
    const avgMemory = memoryMetrics.length > 0
      ? memoryMetrics.reduce((sum, m) => sum + Number(m.metricValue), 0) / memoryMetrics.length
      : 0;
    const avgDisk = diskMetrics.length > 0
      ? diskMetrics.reduce((sum, m) => sum + Number(m.metricValue), 0) / diskMetrics.length
      : 0;

    return {
      cpu: Math.round(avgCpu * 100) / 100,
      memory: Math.round(avgMemory * 100) / 100,
      disk: Math.round(avgDisk * 100) / 100
    };
  }
}
