import { Prisma } from '@prisma/client';
export declare class SecurityService {
    getIPSecurityOverview(): Promise<{
        summary: {
            totalIPs: number;
            blockedIPs: number;
            riskIPs: number;
            activeRules: number;
        };
        recentActivity: ({
            user: {
                username: string;
            } | null;
        } & {
            id: number;
            createdAt: Date;
            userId: number | null;
            actionType: string;
            ipAddress: string;
            userAgent: string | null;
            referer: string | null;
            isBlocked: boolean;
            ruleId: number | null;
            riskScore: number;
            blockExpiresAt: Date | null;
        })[];
        activeRules: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            isActive: boolean;
            ruleName: string;
            ruleType: string;
            timeWindow: number;
            maxAttempts: number;
            blockDuration: number;
        }[];
    }>;
    getIPRiskLogs(options?: {
        page?: number;
        limit?: number;
        ipAddress?: string;
        actionType?: string;
        isBlocked?: boolean;
        startDate?: Date;
        endDate?: Date;
    }): Promise<{
        logs: ({
            user: {
                id: number;
                username: string;
                userLevel: string;
            } | null;
            rule: {
                id: number;
                ruleName: string;
                ruleType: string;
            } | null;
        } & {
            id: number;
            createdAt: Date;
            userId: number | null;
            actionType: string;
            ipAddress: string;
            userAgent: string | null;
            referer: string | null;
            isBlocked: boolean;
            ruleId: number | null;
            riskScore: number;
            blockExpiresAt: Date | null;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getIPRiskRules(): Promise<{
        recentTriggers: number;
        _count: {
            ipRiskLogs: number;
        };
        id: number;
        createdAt: Date;
        updatedAt: Date;
        isActive: boolean;
        ruleName: string;
        ruleType: string;
        timeWindow: number;
        maxAttempts: number;
        blockDuration: number;
    }[]>;
    createIPRiskRule(data: {
        ruleName: string;
        ruleType: string;
        timeWindow: number;
        maxAttempts: number;
        blockDuration: number;
        isActive?: boolean;
    }): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        isActive: boolean;
        ruleName: string;
        ruleType: string;
        timeWindow: number;
        maxAttempts: number;
        blockDuration: number;
    }>;
    updateIPRiskRule(id: number, data: {
        ruleName?: string;
        ruleType?: string;
        timeWindow?: number;
        maxAttempts?: number;
        blockDuration?: number;
        isActive?: boolean;
    }): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        isActive: boolean;
        ruleName: string;
        ruleType: string;
        timeWindow: number;
        maxAttempts: number;
        blockDuration: number;
    }>;
    deleteIPRiskRule(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        isActive: boolean;
        ruleName: string;
        ruleType: string;
        timeWindow: number;
        maxAttempts: number;
        blockDuration: number;
    }>;
    blockIP(data: {
        ipAddress: string;
        blockDuration: number;
        reason: string;
        adminId: number;
    }): Promise<{
        id: number;
        createdAt: Date;
        userId: number | null;
        actionType: string;
        ipAddress: string;
        userAgent: string | null;
        referer: string | null;
        isBlocked: boolean;
        ruleId: number | null;
        riskScore: number;
        blockExpiresAt: Date | null;
    }>;
    unblockIP(ipAddress: string, adminId: number): Promise<{
        id: number;
        createdAt: Date;
        userId: number | null;
        actionType: string;
        ipAddress: string;
        userAgent: string | null;
        referer: string | null;
        isBlocked: boolean;
        ruleId: number | null;
        riskScore: number;
        blockExpiresAt: Date | null;
    }>;
    getUserPermissionsOverview(): Promise<{
        summary: {
            totalUsers: number;
            adminUsers: number;
            vipUsers: number;
            bannedUsers: number;
        };
        recentChanges: ({
            user: {
                username: string;
            };
        } & {
            id: number;
            createdAt: Date;
            userId: number;
            newLevel: string;
            reason: string | null;
            oldLevel: string | null;
            changedBy: number | null;
        })[];
    }>;
    getUserSessions(options?: {
        page?: number;
        limit?: number;
        userId?: number;
        ipAddress?: string;
        isActive?: boolean;
    }): Promise<{
        sessions: {
            isActive: boolean;
            user: {
                id: number;
                username: string;
                role: string;
                userLevel: string;
            };
            id: number;
            createdAt: Date;
            userId: number;
            ipAddress: string | null;
            userAgent: string | null;
            expiresAt: Date;
            tokenHash: string;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    revokeUserSession(sessionId: number, adminId: number): Promise<{
        user: {
            username: string;
        };
    } & {
        id: number;
        createdAt: Date;
        userId: number;
        ipAddress: string | null;
        userAgent: string | null;
        expiresAt: Date;
        tokenHash: string;
    }>;
    getSecurityConfigs(): Promise<{
        description: string | null;
        id: number;
        updatedAt: Date;
        isActive: boolean;
        configKey: string;
        configValue: Prisma.JsonValue;
        updatedBy: number | null;
    }[]>;
    updateSecurityConfig(configKey: string, configValue: any, adminId: number): Promise<{
        description: string | null;
        id: number;
        updatedAt: Date;
        isActive: boolean;
        configKey: string;
        configValue: Prisma.JsonValue;
        updatedBy: number | null;
    }>;
    private getTotalUniqueIPs;
    private getBlockedIPsCount;
    private getRiskIPsCount;
    private getRecentIPActivity;
    private getActiveRiskRules;
    private getRecentPermissionChanges;
    private logSecurityAction;
}
//# sourceMappingURL=security.service.d.ts.map