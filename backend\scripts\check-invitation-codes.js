/**
 * 检查数据库中的邀请码
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkInvitationCodes() {
  try {
    console.log('🔍 检查数据库中的邀请码...\n');

    // 查询所有邀请码
    const invitationCodes = await prisma.invitationCode.findMany({
      include: {
        creator: {
          select: {
            id: true,
            username: true
          }
        },
        user: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 找到 ${invitationCodes.length} 个邀请码:`);
    
    if (invitationCodes.length === 0) {
      console.log('❌ 数据库中没有邀请码');
    } else {
      invitationCodes.forEach((code, index) => {
        console.log(`\n${index + 1}. 邀请码信息:`);
        console.log(`   ID: ${code.id}`);
        console.log(`   Code: ${code.code}`);
        console.log(`   是否已使用: ${code.isUsed ? '是' : '否'}`);
        console.log(`   创建者: ${code.creator.username} (ID: ${code.creator.id})`);
        console.log(`   创建时间: ${code.createdAt}`);
        console.log(`   过期时间: ${code.expiresAt || '永不过期'}`);
        console.log(`   描述: ${code.description || '无'}`);
        
        if (code.isUsed && code.user) {
          console.log(`   使用者: ${code.user.username} (${code.user.email})`);
          console.log(`   使用时间: ${code.usedAt}`);
        }
      });
    }

    // 检查系统配置
    console.log('\n🔧 检查邀请码系统配置...');
    const config = await prisma.systemConfig.findUnique({
      where: { key: 'invitation_system' }
    });

    if (config) {
      console.log('📋 系统配置:');
      console.log(JSON.stringify(config.value, null, 2));
    } else {
      console.log('❌ 没有找到邀请码系统配置');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行检查
checkInvitationCodes();
