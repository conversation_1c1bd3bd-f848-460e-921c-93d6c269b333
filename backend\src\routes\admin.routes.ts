import { Router } from 'express';
import { authenticateToken, requireAdmin } from '../middleware/auth.middleware';
import { AdminController } from '../controllers/admin.controller';

const router = Router();

// 所有管理端路由都需要管理员权限
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * @route   GET /api/admin/stats
 * @desc    获取系统统计数据
 * @access  Admin
 */
router.get('/stats', AdminController.getSystemStats);

/**
 * @route   GET /api/admin/users
 * @desc    获取用户列表
 * @access  Admin
 */
router.get('/users', AdminController.getUsers);

/**
 * @route   PUT /api/admin/users/:id/level
 * @desc    更新用户等级
 * @access  Admin
 */
router.put('/users/:id/level', AdminController.updateUserLevel);

/**
 * @route   PUT /api/admin/users/:userId/status
 * @desc    更新用户状态
 * @access  Admin
 */
router.put('/users/:userId/status', AdminController.updateUserStatus);

/**
 * @route   PUT /api/admin/users/:userId/profile
 * @desc    更新用户基本信息
 * @access  Admin
 */
router.put('/users/:userId/profile', AdminController.updateUserProfile);

/**
 * @route   GET /api/admin/users/:userId/activity-logs
 * @desc    获取用户活动日志
 * @access  Admin
 */
router.get('/users/:userId/activity-logs', AdminController.getUserActivityLogs);

/**
 * @route   GET /api/admin/users/:userId/login-history
 * @desc    获取用户登录历史
 * @access  Admin
 */
router.get('/users/:userId/login-history', AdminController.getUserLoginHistory);

/**
 * @route   GET /api/admin/level-applications
 * @desc    获取等级申请列表
 * @access  Admin
 */
router.get('/level-applications', AdminController.getLevelApplications);

/**
 * @route   PUT /api/admin/level-applications/:applicationId/process
 * @desc    处理等级申请（批准或拒绝）
 * @access  Admin
 */
router.put('/level-applications/:applicationId/process', AdminController.processLevelApplication);

/**
 * @route   DELETE /api/admin/level-applications/:applicationId
 * @desc    删除已完成的等级申请记录
 * @access  Admin
 */
router.delete('/level-applications/:applicationId', AdminController.deleteLevelApplication);

/**
 * @route   GET /api/admin/users/:userId
 * @desc    获取用户详情
 * @access  Admin
 */
router.get('/users/:userId', AdminController.getUserDetail);

/**
 * @route   GET /api/admin/logs/realtime
 * @desc    获取实时日志
 * @access  Admin
 */
router.get('/logs/realtime', AdminController.getRealtimeLogs);

/**
 * @route   GET /api/admin/logs/system
 * @desc    获取系统日志
 * @access  Admin
 */
router.get('/logs/system', AdminController.getSystemLogs);

/**
 * @route   GET /api/admin/providers
 * @desc    获取接口提供商列表
 * @access  Admin
 */
router.get('/providers', AdminController.getProviders);

/**
 * @route   POST /api/admin/providers
 * @desc    添加新的接口提供商
 * @access  Admin
 */
router.post('/providers', AdminController.createProvider);

/**
 * @route   PUT /api/admin/providers/:id
 * @desc    更新接口提供商
 * @access  Admin
 */
router.put('/providers/:id', AdminController.updateProvider);

/**
 * @route   DELETE /api/admin/providers/:id
 * @desc    删除接口提供商
 * @access  Admin
 */
router.delete('/providers/:id', AdminController.deleteProvider);

/**
 * @route   POST /api/admin/providers/:id/test
 * @desc    测试接口提供商连接
 * @access  Admin
 */
router.post('/providers/:id/test', AdminController.testProvider);

/**
 * @route   GET /api/admin/monitoring/system
 * @desc    获取系统监控数据
 * @access  Admin
 */
router.get('/monitoring/system', AdminController.getSystemMonitoring);

/**
 * @route   GET /api/admin/monitoring/metrics/:type
 * @desc    获取特定类型的历史指标数据
 * @access  Admin
 */
router.get('/monitoring/metrics/:type', AdminController.getMetricsHistory);

/**
 * @route   GET /api/admin/monitoring/events
 * @desc    获取系统事件日志
 * @access  Admin
 */
router.get('/monitoring/events', AdminController.getSystemEvents);

/**
 * @route   PUT /api/admin/monitoring/thresholds
 * @desc    更新告警阈值配置
 * @access  Admin
 */
router.put('/monitoring/thresholds', AdminController.updateAlertThresholds);

/**
 * @route   POST /api/admin/monitoring/cleanup
 * @desc    清理过期监控数据
 * @access  Admin
 */
router.post('/monitoring/cleanup', AdminController.cleanupMonitoringData);

/**
 * @route   GET /api/admin/monitoring/retention-config
 * @desc    获取数据保留策略配置
 * @access  Admin
 */
router.get('/monitoring/retention-config', AdminController.getDataRetentionConfig);

/**
 * @route   PUT /api/admin/monitoring/retention-config
 * @desc    更新数据保留策略配置
 * @access  Admin
 */
router.put('/monitoring/retention-config', AdminController.updateDataRetentionConfig);

/**
 * @route   POST /api/admin/monitoring/trigger-cleanup
 * @desc    手动触发数据清理
 * @access  Admin
 */
router.post('/monitoring/trigger-cleanup', AdminController.triggerDataCleanup);

/**
 * @route   DELETE /api/admin/logs/clear
 * @desc    清理系统日志
 * @access  Admin
 */
router.delete('/logs/clear', AdminController.clearSystemLogs);

/**
 * @route   GET /api/admin/security/ips
 * @desc    获取IP安全数据
 * @access  Admin
 */
router.get('/security/ips', AdminController.getIPSecurity);

/**
 * @route   POST /api/admin/security/ips/blacklist
 * @desc    添加IP到黑名单
 * @access  Admin
 */
router.post('/security/ips/blacklist', AdminController.addIPToBlacklist);

/**
 * @route   DELETE /api/admin/security/ips/blacklist/:ip
 * @desc    从黑名单移除IP
 * @access  Admin
 */
router.delete('/security/ips/blacklist/:ip', AdminController.removeIPFromBlacklist);

/**
 * @route   GET /api/admin/analytics/overview
 * @desc    获取数据分析概览
 * @access  Admin
 */
router.get('/analytics/overview', AdminController.getAnalyticsOverview);

/**
 * @route   GET /api/admin/analytics/users
 * @desc    获取用户行为分析
 * @access  Admin
 */
router.get('/analytics/users', AdminController.getUserAnalytics);

/**
 * @route   GET /api/admin/analytics/uploads
 * @desc    获取上传数据分析
 * @access  Admin
 */
router.get('/analytics/uploads', AdminController.getUploadAnalytics);

/**
 * @route   GET /api/admin/analytics/realtime
 * @desc    获取实时统计数据
 * @access  Admin
 */
router.get('/analytics/realtime', AdminController.getRealtimeAnalytics);

/**
 * @route   POST /api/admin/logs/clear-realtime
 * @desc    清除实时系统日志
 * @access  Admin
 */
router.post('/logs/clear-realtime', AdminController.clearRealtimeLogs);

// ==================== 安全管理路由 ====================

/**
 * @route   GET /api/admin/security/ip-overview
 * @desc    获取IP风控概览
 * @access  Admin
 */
router.get('/security/ip-overview', AdminController.getIPSecurityOverview);

/**
 * @route   GET /api/admin/security/ip-logs
 * @desc    获取IP风控日志列表
 * @access  Admin
 */
router.get('/security/ip-logs', AdminController.getIPRiskLogs);

/**
 * @route   GET /api/admin/security/ip-rules
 * @desc    获取IP风控规则列表
 * @access  Admin
 */
router.get('/security/ip-rules', AdminController.getIPRiskRules);

/**
 * @route   POST /api/admin/security/ip-rules
 * @desc    创建IP风控规则
 * @access  Admin
 */
router.post('/security/ip-rules', AdminController.createIPRiskRule);

/**
 * @route   PUT /api/admin/security/ip-rules/:id
 * @desc    更新IP风控规则
 * @access  Admin
 */
router.put('/security/ip-rules/:id', AdminController.updateIPRiskRule);

/**
 * @route   DELETE /api/admin/security/ip-rules/:id
 * @desc    删除IP风控规则
 * @access  Admin
 */
router.delete('/security/ip-rules/:id', AdminController.deleteIPRiskRule);

/**
 * @route   POST /api/admin/security/block-ip
 * @desc    手动封禁IP
 * @access  Admin
 */
router.post('/security/block-ip', AdminController.blockIP);

/**
 * @route   POST /api/admin/security/unblock-ip
 * @desc    解封IP
 * @access  Admin
 */
router.post('/security/unblock-ip', AdminController.unblockIP);

/**
 * @route   GET /api/admin/security/permissions-overview
 * @desc    获取用户权限概览
 * @access  Admin
 */
router.get('/security/permissions-overview', AdminController.getUserPermissionsOverview);

/**
 * @route   GET /api/admin/security/sessions
 * @desc    获取用户会话列表
 * @access  Admin
 */
router.get('/security/sessions', AdminController.getUserSessions);

/**
 * @route   DELETE /api/admin/security/sessions/:sessionId
 * @desc    强制注销用户会话
 * @access  Admin
 */
router.delete('/security/sessions/:sessionId', AdminController.revokeUserSession);

/**
 * @route   GET /api/admin/security/configs
 * @desc    获取安全配置
 * @access  Admin
 */
router.get('/security/configs', AdminController.getSecurityConfigs);

/**
 * @route   PUT /api/admin/security/configs
 * @desc    更新安全配置
 * @access  Admin
 */
router.put('/security/configs', AdminController.updateSecurityConfig);

export default router;
