/**
 * 直接查询数据库中的邀请码
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function directDBCheck() {
  try {
    console.log('🔍 直接查询数据库中的邀请码...\n');

    // 查询所有邀请码
    const allCodes = await prisma.invitationCode.findMany({
      select: {
        id: true,
        code: true,
        isUsed: true,
        createdAt: true,
        description: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 数据库中共有 ${allCodes.length} 个邀请码:`);
    
    allCodes.forEach((code, index) => {
      console.log(`${index + 1}. ID: ${code.id}, Code: "${code.code}", Used: ${code.isUsed}, Created: ${code.createdAt}`);
    });

    if (allCodes.length > 0) {
      // 测试查询第一个邀请码
      const firstCode = allCodes[0];
      console.log(`\n🧪 测试查询邀请码: "${firstCode.code}"`);
      
      const foundCode = await prisma.invitationCode.findUnique({
        where: { code: firstCode.code }
      });

      if (foundCode) {
        console.log('✅ 查询成功，找到邀请码');
        console.log(`   ID: ${foundCode.id}`);
        console.log(`   Code: "${foundCode.code}"`);
        console.log(`   Used: ${foundCode.isUsed}`);
      } else {
        console.log('❌ 查询失败，未找到邀请码');
      }

      // 测试查询一个不存在的邀请码
      console.log(`\n🧪 测试查询不存在的邀请码: "NOTEXIST12345"`);
      const notFoundCode = await prisma.invitationCode.findUnique({
        where: { code: 'NOTEXIST12345' }
      });

      if (notFoundCode) {
        console.log('❌ 意外找到了不存在的邀请码');
      } else {
        console.log('✅ 正确返回null，未找到不存在的邀请码');
      }
    }

  } catch (error) {
    console.error('❌ 数据库查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行检查
directDBCheck();
