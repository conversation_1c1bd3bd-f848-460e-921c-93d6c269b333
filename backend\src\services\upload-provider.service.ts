import { prisma } from '../config/database';
import axios from 'axios';
import FormData from 'form-data';

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
  responseTime?: number;
  providerId: number;
  providerName: string;
}

export interface ProviderConfig {
  id: number;
  name: string;
  endpoint: string;
  apiKey?: string;
  config?: any;
  maxFileSize: number;
  supportedFormats: string[];
  isPremium: boolean;
}

export class UploadProviderService {
  // 获取用户可用的上传接口
  static async getAvailableProviders(userId: number, userLevel: string): Promise<ProviderConfig[]> {
    try {
      // 获取基于用户等级的可见接口
      const levelVisibility = await prisma.levelProviderVisibility.findMany({
        where: {
          level: userLevel,
          isVisible: true,
        },
        include: {
          provider: true
        },
        orderBy: {
          displayOrder: 'asc'
        }
      });

      // 获取用户特殊权限的接口
      const userPermissions = await prisma.userProviderPermission.findMany({
        where: {
          userId,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        include: {
          provider: true
        }
      });

      // 合并接口列表
      const providers = new Map<number, ProviderConfig>();

      // 添加等级可见的接口
      levelVisibility.forEach(lv => {
        if (lv.provider && lv.provider.status === 'active') {
          providers.set(lv.provider.id, {
            id: lv.provider.id,
            name: lv.provider.name,
            endpoint: lv.provider.endpoint,
            apiKey: lv.provider.apiKey || '',
            config: lv.provider.config,
            maxFileSize: Number(lv.provider.maxFileSize),
            supportedFormats: lv.provider.supportedFormats,
            isPremium: lv.provider.isPremium,
          });
        }
      });

      // 添加用户特殊权限的接口
      userPermissions.forEach(up => {
        if (up.provider && up.provider.status === 'active') {
          providers.set(up.provider.id, {
            id: up.provider.id,
            name: up.provider.name,
            endpoint: up.provider.endpoint,
            apiKey: up.provider.apiKey || '',
            config: up.provider.config,
            maxFileSize: Number(up.provider.maxFileSize),
            supportedFormats: up.provider.supportedFormats,
            isPremium: up.provider.isPremium,
          });
        }
      });

      return Array.from(providers.values());
    } catch (error) {
      console.error('获取可用上传接口失败:', error);
      return [];
    }
  }

  // 上传文件到指定接口
  static async uploadToProvider(
    provider: ProviderConfig,
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string
  ): Promise<UploadResult> {
    const startTime = Date.now();

    try {
      let result: UploadResult;

      // 根据接口类型选择上传方法
      switch (provider.name.toLowerCase()) {
        case 'imgur':
          result = await UploadProviderService.uploadToImgur(provider, fileBuffer, fileName);
          break;
        case 'cloudinary':
          result = await UploadProviderService.uploadToCloudinary(provider, fileBuffer, fileName);
          break;
        case 'qiniu':
        case '七牛云':
          result = await UploadProviderService.uploadToQiniu(provider, fileBuffer, fileName, mimeType);
          break;
        case 'custom':
          result = await UploadProviderService.uploadToCustomProvider(provider, fileBuffer, fileName, mimeType);
          break;
        default:
          result = await UploadProviderService.uploadToGenericProvider(provider, fileBuffer, fileName, mimeType);
          break;
      }

      result.responseTime = Date.now() - startTime;
      return result;

    } catch (error) {
      console.error(`上传到 ${provider.name} 失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败',
        responseTime: Date.now() - startTime,
        providerId: provider.id,
        providerName: provider.name,
      };
    }
  }

  // 上传到Imgur
  private static async uploadToImgur(
    provider: ProviderConfig,
    fileBuffer: Buffer,
    fileName: string
  ): Promise<UploadResult> {
    try {
      const formData = new FormData();
      formData.append('image', fileBuffer, fileName);

      const response = await axios.post('https://api.imgur.com/3/image', formData, {
        headers: {
          'Authorization': `Client-ID ${provider.apiKey}`,
          ...formData.getHeaders(),
        },
        timeout: 30000,
      });

      if (response.data.success) {
        return {
          success: true,
          url: response.data.data.link,
          providerId: provider.id,
          providerName: provider.name,
        };
      } else {
        return {
          success: false,
          error: 'Imgur API返回失败',
          providerId: provider.id,
          providerName: provider.name,
        };
      }
    } catch (error) {
      throw new Error(`Imgur上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 上传到Cloudinary
  private static async uploadToCloudinary(
    provider: ProviderConfig,
    fileBuffer: Buffer,
    fileName: string
  ): Promise<UploadResult> {
    try {
      const formData = new FormData();
      formData.append('file', fileBuffer, fileName);
      formData.append('upload_preset', provider.config?.uploadPreset || 'default');

      const cloudName = provider.config?.cloudName;
      const endpoint = `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`;

      const response = await axios.post(endpoint, formData, {
        headers: formData.getHeaders(),
        timeout: 30000,
      });

      return {
        success: true,
        url: response.data.secure_url,
        providerId: provider.id,
        providerName: provider.name,
      };
    } catch (error) {
      throw new Error(`Cloudinary上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 根据ID获取单个接口配置
  static async getProviderById(providerId: number): Promise<ProviderConfig | null> {
    try {
      const provider = await prisma.uploadProvider.findUnique({
        where: {
          id: providerId,
          status: 'active'
        }
      });

      if (!provider) {
        return null;
      }

      return {
        id: provider.id,
        name: provider.name,
        endpoint: provider.endpoint,
        apiKey: provider.apiKey || '',
        config: provider.config,
        maxFileSize: Number(provider.maxFileSize),
        supportedFormats: provider.supportedFormats,
        isPremium: provider.isPremium,
      };
    } catch (error) {
      console.error('获取接口配置失败:', error);
      return null;
    }
  }

  // 上传到七牛云
  private static async uploadToQiniu(
    provider: ProviderConfig,
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string
  ): Promise<UploadResult> {
    try {
      // 获取七牛云token
      const tokenEndpoint = provider.config?.tokenEndpoint || 'https://zgtdxh.kejie.org.cn/ajax/get-qiniu-token.php?prefix=user';

      const tokenResponse = await axios.get(tokenEndpoint, {
        timeout: 10000,
      });

      if (!tokenResponse.data || !tokenResponse.data.data || !tokenResponse.data.data.token) {
        throw new Error('获取七牛云token失败');
      }

      const token = tokenResponse.data.data.token;

      // 生成文件key（按照PHP代码的规则）
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hour = String(now.getHours()).padStart(2, '0');
      const uniqueId = Date.now().toString() + Math.random().toString(36).substring(2, 11);

      // 获取文件扩展名
      const ext = fileName.split('.').pop() || 'png';
      const fileKey = `${year}/${month}/${day}/${hour}/video/${uniqueId}.${ext}`;

      // 构建上传数据
      const formData = new FormData();
      formData.append('name', fileName);
      formData.append('chunk', '0');
      formData.append('chunks', '1');
      formData.append('key', fileKey);
      formData.append('token', token);
      formData.append('file', fileBuffer, { filename: fileName, contentType: mimeType });

      // 上传到七牛云
      const uploadUrl = provider.endpoint || 'https://upload.qbox.me/';
      const response = await axios.post(uploadUrl, formData, {
        headers: formData.getHeaders(),
        timeout: 30000,
      });

      if (response.data && response.data.key) {
        // 构建完整的访问URL
        const baseUrl = provider.config?.baseUrl || 'https://acad-upload.scimall.org.cn/';
        const finalUrl = baseUrl + response.data.key;

        return {
          success: true,
          url: finalUrl,
          providerId: provider.id,
          providerName: provider.name,
        };
      } else {
        return {
          success: false,
          error: '七牛云上传失败或响应格式错误',
          providerId: provider.id,
          providerName: provider.name,
        };
      }
    } catch (error) {
      throw new Error(`七牛云上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 上传到自定义接口
  private static async uploadToCustomProvider(
    provider: ProviderConfig,
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string
  ): Promise<UploadResult> {
    try {
      const formData = new FormData();
      formData.append('file', fileBuffer, { filename: fileName, contentType: mimeType });

      // 添加自定义配置参数
      if (provider.config) {
        Object.keys(provider.config).forEach(key => {
          if (key !== 'headers') {
            formData.append(key, provider.config[key]);
          }
        });
      }

      const headers: any = {
        ...formData.getHeaders(),
      };

      // 添加API密钥
      if (provider.apiKey) {
        headers['Authorization'] = `Bearer ${provider.apiKey}`;
      }

      // 添加自定义头部
      if (provider.config?.headers) {
        Object.assign(headers, provider.config.headers);
      }

      const response = await axios.post(provider.endpoint, formData, {
        headers,
        timeout: 30000,
      });

      // 根据配置解析响应
      const urlPath = provider.config?.responseUrlPath || 'url';
      const url = UploadProviderService.getNestedProperty(response.data, urlPath);

      if (url) {
        return {
          success: true,
          url,
          providerId: provider.id,
          providerName: provider.name,
        };
      } else {
        return {
          success: false,
          error: '无法从响应中提取URL',
          providerId: provider.id,
          providerName: provider.name,
        };
      }
    } catch (error) {
      throw new Error(`自定义接口上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 通用接口上传
  private static async uploadToGenericProvider(
    provider: ProviderConfig,
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string
  ): Promise<UploadResult> {
    try {
      const formData = new FormData();
      formData.append('file', fileBuffer, { filename: fileName, contentType: mimeType });

      const headers: any = {
        ...formData.getHeaders(),
      };

      if (provider.apiKey) {
        headers['Authorization'] = `Bearer ${provider.apiKey}`;
      }

      const response = await axios.post(provider.endpoint, formData, {
        headers,
        timeout: 30000,
      });

      // 尝试从常见的响应字段中提取URL
      const possibleUrlFields = ['url', 'link', 'src', 'image_url', 'file_url', 'data.url', 'result.url'];
      let url: string | undefined;

      for (const field of possibleUrlFields) {
        url = UploadProviderService.getNestedProperty(response.data, field);
        if (url) break;
      }

      if (url) {
        return {
          success: true,
          url,
          providerId: provider.id,
          providerName: provider.name,
        };
      } else {
        return {
          success: false,
          error: '无法从响应中提取URL',
          providerId: provider.id,
          providerName: provider.name,
        };
      }
    } catch (error) {
      throw new Error(`通用接口上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 批量上传到多个接口
  static async uploadToMultipleProviders(
    providers: ProviderConfig[],
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string
  ): Promise<UploadResult[]> {
    const uploadPromises = providers.map(provider =>
      UploadProviderService.uploadToProvider(provider, fileBuffer, fileName, mimeType)
    );

    try {
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('批量上传失败:', error);
      return [];
    }
  }

  // 保存上传结果到数据库
  static async saveUploadResults(imageId: number, results: UploadResult[]): Promise<void> {
    try {
      const linkData = results
        .filter(result => result.success && result.url)
        .map(result => ({
          imageId,
          providerId: result.providerId,
          externalUrl: result.url!,
          status: 'active' as const,
          responseTime: result.responseTime ?? null,
          lastChecked: new Date(),
        }));

      if (linkData.length > 0) {
        await prisma.imageLink.createMany({
          data: linkData,
        });
      }

      // 更新图片状态
      const hasSuccessfulUpload = results.some(result => result.success);
      await prisma.image.update({
        where: { id: imageId },
        data: {
          uploadStatus: hasSuccessfulUpload ? 'completed' : 'failed',
        }
      });
    } catch (error) {
      console.error('保存上传结果失败:', error);
    }
  }

  // 检查接口可用性
  static async checkProviderHealth(provider: ProviderConfig): Promise<boolean> {
    try {
      // 创建一个1x1像素的测试图片
      const testImageBuffer = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );

      const result = await UploadProviderService.uploadToProvider(
        provider,
        testImageBuffer,
        'test.png',
        'image/png'
      );

      return result.success;
    } catch {
      return false;
    }
  }

  // 获取嵌套属性值
  private static getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  // 验证接口配置
  static validateProviderConfig(provider: ProviderConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!provider.name) {
      errors.push('接口名称不能为空');
    }

    if (!provider.endpoint) {
      errors.push('接口地址不能为空');
    }

    if (provider.maxFileSize <= 0) {
      errors.push('最大文件大小必须大于0');
    }

    if (!provider.supportedFormats || provider.supportedFormats.length === 0) {
      errors.push('必须指定支持的文件格式');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
