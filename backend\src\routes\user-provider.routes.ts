import { Router } from 'express';
import { UserProviderController } from '../controllers/user-provider.controller';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

// 获取用户可用的接口列表
router.get('/available', authenticateToken, UserProviderController.getAvailableProviders);

// 获取用户接口使用统计
router.get('/stats', authenticateToken, UserProviderController.getUserProviderStats);

// 管理员路由 - 授权用户接口权限
router.post('/grant', authenticateToken, UserProviderController.grantProviderPermission);

// 管理员路由 - 撤销用户接口权限
router.post('/revoke', authenticateToken, UserProviderController.revokeProviderPermission);

// 管理员路由 - 初始化等级接口可见性配置
router.post('/initialize-visibility', authenticateToken, UserProviderController.initializeLevelProviderVisibility);

export default router;
