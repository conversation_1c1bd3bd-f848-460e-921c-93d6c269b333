# LoftChat 多端口兼容性配置

## 概述

LoftChat 系统现已支持前端5173端口兼容后端3000、3001、3002端口的自动检测和切换功能。

## 功能特性

### 🔄 自动端口检测
- 前端启动时自动检测可用的后端端口
- 支持3000、3001、3002三个后端端口
- 按优先级顺序检测：3000 → 3001 → 3002

### 🚀 动态端口切换
- 实时监控后端服务健康状态
- 当前端口不可用时自动切换到其他可用端口
- 30秒间隔的健康检查机制

### 📡 智能代理配置
- Vite开发服务器自动配置API代理
- Socket.IO连接自动跟随端口切换
- 错误重试和故障转移机制

## 端口配置

### 后端端口
- **3000**: 主要后端服务端口
- **3001**: 备用后端服务端口  
- **3002**: 第三备用后端服务端口

### 前端端口
- **5173**: 主要前端服务端口
- **5174**: 备用前端服务端口（当5173被占用时）
- **5175**: 第三备用前端服务端口

## 启动方式

### 1. 启动单个后端服务
```bash
# 启动在3001端口
cd backend
PORT=3001 npm run dev

# 或者修改 .env 文件
echo "PORT=3001" > backend/.env
cd backend && npm run dev
```

### 2. 启动多个后端服务
```bash
# 终端1 - 启动3000端口
cd backend
PORT=3000 npm run dev

# 终端2 - 启动3001端口  
cd backend
PORT=3001 npm run dev

# 终端3 - 启动3002端口
cd backend  
PORT=3002 npm run dev
```

### 3. 启动前端服务
```bash
cd frontend
npm run dev
```

前端会自动检测并连接到可用的后端端口。

## 测试多端口功能

### 1. 访问测试页面
打开浏览器访问：`http://localhost:5174/test/user-providers`

### 2. 测试端口切换
1. 启动多个后端服务
2. 观察前端控制台的端口检测日志
3. 停止当前连接的后端服务
4. 观察前端是否自动切换到其他可用端口

### 3. API测试
```bash
# 测试3001端口
curl "http://localhost:3001/api/user-providers/available?userId=1"

# 测试3002端口  
curl "http://localhost:3002/api/user-providers/available?userId=1"
```

## 配置文件

### 前端配置 (`frontend/vite.config.ts`)
- 动态后端端口检测
- 代理配置自动更新
- 错误重试机制

### 前端API配置 (`frontend/src/config/api.config.ts`)
- 端口优先级列表
- 健康检查间隔设置
- 自动故障转移逻辑

### 后端配置 (`backend/.env`)
```env
PORT=3001
NODE_ENV=development
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
```

## 监控和日志

### 前端日志
- 端口检测结果
- 连接状态变化
- API请求重试信息

### 后端日志
- 服务启动端口信息
- 健康检查响应
- API请求处理状态

## 故障排除

### 常见问题

1. **前端无法连接后端**
   - 检查后端服务是否启动
   - 确认端口没有被其他程序占用
   - 查看前端控制台的错误信息

2. **端口切换不生效**
   - 等待30秒健康检查周期
   - 手动刷新页面触发重新检测
   - 检查网络连接状态

3. **API请求失败**
   - 确认后端服务正常运行
   - 检查API端点是否正确
   - 查看后端日志的错误信息

### 调试命令
```bash
# 检查端口占用情况
netstat -ano | findstr :3000
netstat -ano | findstr :3001  
netstat -ano | findstr :3002

# 测试端口连通性
curl http://localhost:3001/health
curl http://localhost:3002/health
```

## 技术实现

### 核心组件
1. **ApiEndpointManager**: 端口管理和健康检查
2. **Vite代理配置**: 动态代理目标更新
3. **UserProviderService**: API服务层适配

### 关键特性
- TypeScript类型安全
- 错误边界处理
- 性能优化的健康检查
- 用户友好的状态提示

## 扩展性

系统设计支持轻松添加更多端口：

1. 在 `BACKEND_PORTS` 数组中添加新端口
2. 更新健康检查逻辑（如需要）
3. 配置负载均衡（可选）

这种设计为开发、测试和生产环境提供了灵活的部署选项。
