/**
 * 测试数据分析API接口
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001/api/admin';

// 模拟管理员token（实际使用中需要真实的JWT token）
const ADMIN_TOKEN = 'test-admin-token';

async function testAPI(endpoint, description) {
  try {
    console.log(`\n🔍 测试: ${description}`);
    console.log(`📡 请求: GET ${BASE_URL}${endpoint}`);
    
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ 成功: ${response.status}`);
      console.log(`📊 数据结构:`, Object.keys(data.data || {}));
      
      // 显示一些关键数据
      if (data.data) {
        if (data.data.summary) {
          console.log(`📈 摘要数据:`, {
            用户: data.data.summary.users?.total || 'N/A',
            上传: data.data.summary.uploads?.total || 'N/A',
            访问: data.data.summary.access?.total || 'N/A'
          });
        }
        if (data.data.timeRange) {
          console.log(`⏰ 时间范围: ${data.data.timeRange}`);
        }
      }
    } else {
      console.log(`❌ 失败: ${response.status}`);
      console.log(`💬 错误信息:`, data.error?.message || data.message || '未知错误');
    }
    
    return { success: response.ok, data };
  } catch (error) {
    console.log(`💥 请求异常:`, error.message);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 开始测试数据分析API接口...\n');
  
  const tests = [
    {
      endpoint: '/analytics/overview?timeRange=month',
      description: '数据分析概览 (月度)'
    },
    {
      endpoint: '/analytics/overview?timeRange=week',
      description: '数据分析概览 (周度)'
    },
    {
      endpoint: '/analytics/users?timeRange=month',
      description: '用户行为分析 (月度)'
    },
    {
      endpoint: '/analytics/uploads?timeRange=month',
      description: '上传数据分析 (月度)'
    },
    {
      endpoint: '/analytics/realtime',
      description: '实时统计数据'
    }
  ];

  let successCount = 0;
  let totalCount = tests.length;

  for (const test of tests) {
    const result = await testAPI(test.endpoint, test.description);
    if (result.success) {
      successCount++;
    }
    
    // 等待一秒避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 成功: ${successCount}/${totalCount}`);
  console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`);
  console.log(`📈 成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`);

  if (successCount === totalCount) {
    console.log('\n🎉 所有API接口测试通过！数据分析功能正常工作。');
  } else {
    console.log('\n⚠️  部分API接口测试失败，请检查后端服务和数据库连接。');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testAPI, runTests };
