{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/admin/adminoverview.tsx", "../../src/components/admin/analyticsmanagement.tsx", "../../src/components/admin/enhanceduserdetailmodal.tsx", "../../src/components/admin/invitationcodemanagement.tsx", "../../src/components/admin/levelapplicationmanagement.tsx", "../../src/components/admin/logsmanagement.tsx", "../../src/components/admin/providermanagement.tsx", "../../src/components/admin/securitymanagement.tsx", "../../src/components/admin/systemmonitoring.tsx", "../../src/components/admin/userdetailmodal.tsx", "../../src/components/admin/usermanagement.tsx", "../../src/components/auth/loginform.tsx", "../../src/components/auth/registerform.tsx", "../../src/components/charts/areachart.tsx", "../../src/components/charts/barchart.tsx", "../../src/components/charts/linechart.tsx", "../../src/components/charts/piechart.tsx", "../../src/components/charts/index.ts", "../../src/components/settings/activitylogs.tsx", "../../src/components/settings/notificationsettings.tsx", "../../src/components/settings/passwordsettings.tsx", "../../src/components/settings/profilesettings.tsx", "../../src/components/settings/uploadsettings.tsx", "../../src/components/settings/usersettings.tsx", "../../src/components/stats/statsoverview.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/modal.tsx", "../../src/components/ui/switch.tsx", "../../src/components/ui/toast.tsx", "../../src/components/upload/fileupload.tsx", "../../src/components/upload/uploadhistory.tsx", "../../src/components/user/levelupgrade.tsx", "../../src/config/api.config.ts", "../../src/config/api.ts", "../../src/contexts/authcontext.tsx", "../../src/lib/api.ts", "../../src/lib/utils.ts", "../../src/pages/admindashboard.tsx", "../../src/pages/authpage.tsx", "../../src/pages/dashboard.tsx", "../../src/pages/settings.tsx", "../../src/services/admin.service.ts", "../../src/services/invitation-code.service.ts", "../../src/services/socket.service.ts", "../../src/services/user.service.ts", "../../src/types/index.ts", "../../src/utils/api.utils.ts"], "errors": true, "version": "5.8.3"}