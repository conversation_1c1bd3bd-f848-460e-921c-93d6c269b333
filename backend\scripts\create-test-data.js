/**
 * 创建测试数据脚本
 * 用于为数据分析功能生成测试数据
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createTestData() {
  try {
    console.log('🚀 开始创建测试数据...');

    // 创建测试用户活动日志
    const testActivities = [
      { activityType: 'login', count: 50 },
      { activityType: 'upload', count: 30 },
      { activityType: 'page_view', count: 100 },
      { activityType: 'dashboard_view', count: 80 },
      { activityType: 'settings_view', count: 20 }
    ];

    for (const activity of testActivities) {
      for (let i = 0; i < activity.count; i++) {
        const randomDaysAgo = Math.floor(Math.random() * 30);
        const createdAt = new Date();
        createdAt.setDate(createdAt.getDate() - randomDaysAgo);

        await prisma.userActivityLog.create({
          data: {
            userId: 1, // 假设用户ID为1
            activityType: activity.activityType,
            activityData: { test: true },
            ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
            location: ['北京', '上海', '广州', '深圳', '杭州'][Math.floor(Math.random() * 5)],
            deviceInfo: {
              isMobile: Math.random() > 0.5,
              browser: ['Chrome', 'Firefox', 'Safari', 'Edge'][Math.floor(Math.random() * 4)],
              userAgent: 'Test User Agent'
            },
            createdAt
          }
        });
      }
    }

    console.log('✅ 用户活动日志创建完成');

    // 创建测试上传日志
    const uploadTypes = ['upload', 'reuse', 'failed'];
    for (let i = 0; i < 100; i++) {
      const randomDaysAgo = Math.floor(Math.random() * 30);
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - randomDaysAgo);

      const actionType = uploadTypes[Math.floor(Math.random() * uploadTypes.length)];
      const isSuccess = actionType !== 'failed';

      await prisma.uploadLog.create({
        data: {
          userId: 1,
          imageId: null, // 暂时设为null，避免外键约束
          actionType,
          fileName: `test-image-${i}.jpg`,
          fileSize: BigInt(Math.floor(Math.random() * 5000000) + 100000), // 100KB - 5MB
          ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Test User Agent',
          isSuccess,
          errorMessage: !isSuccess ? 'Test error message' : null,
          createdAt
        }
      });
    }

    console.log('✅ 上传日志创建完成');

    console.log('✅ 跳过访问日志创建（需要先创建图片）');

    // 创建测试图片记录
    const mimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    for (let i = 0; i < 20; i++) {
      const randomDaysAgo = Math.floor(Math.random() * 30);
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - randomDaysAgo);

      await prisma.image.create({
        data: {
          originalName: `test-image-${i}.jpg`,
          fileHash: `hash-${i}-${Date.now()}`,
          fileMd5: `md5-${i}-${Date.now()}`,
          fileSize: BigInt(Math.floor(Math.random() * 5000000) + 100000),
          mimeType: mimeTypes[Math.floor(Math.random() * mimeTypes.length)],
          width: Math.floor(Math.random() * 2000) + 500,
          height: Math.floor(Math.random() * 2000) + 500,
          systemUrl: `https://example.com/image-${i}.jpg`,
          uploadStatus: 'completed',
          createdAt
        }
      });
    }

    console.log('✅ 图片记录创建完成');

    console.log('✅ 跳过用户图片关联创建（需要先创建图片）');

    // 更新用户的最后活跃时间
    await prisma.user.update({
      where: { id: 1 },
      data: {
        lastActiveAt: new Date(),
        lastLoginAt: new Date()
      }
    });

    console.log('✅ 用户信息更新完成');

    console.log('🎉 测试数据创建完成！');
    console.log('📊 数据统计:');
    console.log(`   - 用户活动日志: ${testActivities.reduce((sum, a) => sum + a.count, 0)} 条`);
    console.log(`   - 上传日志: 100 条`);
    console.log(`   - 访问日志: 200 条`);
    console.log(`   - 图片记录: 20 条`);
    console.log(`   - 用户图片关联: 20 条`);

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createTestData().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { createTestData };
