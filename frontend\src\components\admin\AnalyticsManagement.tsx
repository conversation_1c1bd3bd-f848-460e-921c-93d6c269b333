import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, formatFileSize, formatNumber, formatDate, CHART_COLORS } from '../charts';
import { adminService } from '../../services/admin.service';
import type { AnalyticsOverview, UserAnalytics, UploadAnalytics, RealtimeStats } from '../../services/admin.service';

type TimeRange = 'day' | 'week' | 'month' | 'year';
type AnalyticsTab = 'overview' | 'users' | 'uploads';

export function AnalyticsManagement() {
  const [activeTab, setActiveTab] = useState<AnalyticsTab>('overview');
  const [timeRange, setTimeRange] = useState<TimeRange>('month');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 数据状态
  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics | null>(null);
  const [uploadAnalytics, setUploadAnalytics] = useState<UploadAnalytics | null>(null);
  const [realtimeStats, setRealtimeStats] = useState<RealtimeStats | null>(null);

  useEffect(() => {
    loadAnalyticsData();

    // 设置定时刷新实时数据
    const interval = setInterval(() => {
      loadRealtimeStats();
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(interval);
  }, [timeRange]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [overviewData, userData, uploadData, realtimeData] = await Promise.all([
        adminService.getAnalyticsOverview(timeRange),
        adminService.getUserAnalytics(timeRange),
        adminService.getUploadAnalytics(timeRange),
        adminService.getRealtimeAnalytics()
      ]);

      setOverview(overviewData);
      setUserAnalytics(userData);
      setUploadAnalytics(uploadData);
      setRealtimeStats(realtimeData);
    } catch (error) {
      console.error('加载数据分析失败:', error);
      setError('加载数据分析失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const loadRealtimeStats = async () => {
    try {
      const realtimeData = await adminService.getRealtimeAnalytics();
      setRealtimeStats(realtimeData);
    } catch (error) {
      console.error('刷新实时数据失败:', error);
    }
  };

  const timeRangeOptions = [
    { value: 'day' as TimeRange, label: '今日' },
    { value: 'week' as TimeRange, label: '本周' },
    { value: 'month' as TimeRange, label: '本月' },
    { value: 'year' as TimeRange, label: '本年' }
  ];

  const tabs = [
    { key: 'overview' as AnalyticsTab, label: '数据概览', icon: '📊' },
    { key: 'users' as AnalyticsTab, label: '用户分析', icon: '👥' },
    { key: 'uploads' as AnalyticsTab, label: '上传分析', icon: '📁' }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
            <p className="text-gray-600">加载数据分析中...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={loadAnalyticsData}>重新加载</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和控制栏 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-xl font-semibold text-gray-900">数据分析</h1>
          <p className="text-sm text-gray-600 mt-1">系统数据统计与分析报告</p>
        </div>

        <div className="flex items-center gap-3">
          {/* 时间范围选择 */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {timeRangeOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => setTimeRange(option.value)}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                  timeRange === option.value
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>

          <Button
            onClick={loadAnalyticsData}
            size="sm"
            variant="outline"
          >
            刷新数据
          </Button>
        </div>
      </div>

      {/* 实时统计卡片 */}
      {realtimeStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-600">今日上传</p>
                  <p className="text-lg font-semibold text-gray-900">{realtimeStats.uploads.today}</p>
                  <p className="text-xs text-gray-500">
                    昨日: {realtimeStats.uploads.yesterday}
                  </p>
                </div>
                <div className={`text-xs px-2 py-1 rounded-full ${
                  realtimeStats.uploads.growth >= 0
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {realtimeStats.uploads.growth >= 0 ? '+' : ''}{realtimeStats.uploads.growth.toFixed(1)}%
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-600">在线用户</p>
                  <p className="text-lg font-semibold text-gray-900">{realtimeStats.users.online}</p>
                  <p className="text-xs text-gray-500">
                    新增: {realtimeStats.users.newToday}
                  </p>
                </div>
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div>
                <p className="text-xs text-gray-600">CPU使用率</p>
                <p className="text-lg font-semibold text-gray-900">{realtimeStats.system.cpu.toFixed(1)}%</p>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                  <div
                    className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(realtimeStats.system.cpu, 100)}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div>
                <p className="text-xs text-gray-600">内存使用率</p>
                <p className="text-lg font-semibold text-gray-900">{realtimeStats.system.memory.toFixed(1)}%</p>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                  <div
                    className="bg-green-600 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(realtimeStats.system.memory, 100)}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 标签页导航 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      {activeTab === 'overview' && overview && (
        <OverviewTab overview={overview} />
      )}

      {activeTab === 'users' && userAnalytics && (
        <UsersTab userAnalytics={userAnalytics} />
      )}

      {activeTab === 'uploads' && uploadAnalytics && (
        <UploadsTab uploadAnalytics={uploadAnalytics} />
      )}
    </div>
  );
}

// 数据概览标签页
function OverviewTab({ overview }: { overview: AnalyticsOverview }) {
  return (
    <div className="space-y-6">
      {/* 统计摘要 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">总用户数</p>
                <p className="text-xl font-bold text-gray-900">{formatNumber(overview.summary.users.total)}</p>
                <p className="text-xs text-green-600">+{overview.summary.users.new} 新增</p>
              </div>
              <div className="text-2xl">👥</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">总上传量</p>
                <p className="text-xl font-bold text-gray-900">{formatNumber(overview.summary.uploads.total)}</p>
                <p className="text-xs text-green-600">+{overview.summary.uploads.new} 新增</p>
              </div>
              <div className="text-2xl">📁</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">访问量</p>
                <p className="text-xl font-bold text-gray-900">{formatNumber(overview.summary.access.total)}</p>
                <p className="text-xs text-blue-600">{overview.summary.access.uniqueVisitors} UV</p>
              </div>
              <div className="text-2xl">👁️</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">存储使用</p>
                <p className="text-xl font-bold text-gray-900">{formatFileSize(overview.summary.storage.totalSize)}</p>
                <p className="text-xs text-gray-600">{overview.summary.storage.totalFiles} 文件</p>
              </div>
              <div className="text-2xl">💾</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 趋势图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">上传趋势</CardTitle>
          </CardHeader>
          <CardContent>
            <AreaChart
              data={overview.trends.uploads}
              dataKey="count"
              xAxisKey="date"
              color={CHART_COLORS.primary}
              height={250}
              formatXAxis={formatDate}
              formatTooltip={(value, name) => [value.toString(), '上传量']}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">用户增长</CardTitle>
          </CardHeader>
          <CardContent>
            <AreaChart
              data={overview.trends.users}
              dataKey="count"
              xAxisKey="date"
              color={CHART_COLORS.success}
              height={250}
              formatXAxis={formatDate}
              formatTooltip={(value, name) => [value.toString(), '新增用户']}
            />
          </CardContent>
        </Card>
      </div>

      {/* 访问趋势 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">访问趋势对比</CardTitle>
        </CardHeader>
        <CardContent>
          <LineChart
            data={overview.trends.uploads.map((item, index) => ({
              date: item.date,
              uploads: item.count,
              users: overview.trends.users[index]?.count || 0,
              access: overview.trends.access[index]?.count || 0
            }))}
            lines={[
              { dataKey: 'uploads', color: CHART_COLORS.primary, name: '上传量' },
              { dataKey: 'users', color: CHART_COLORS.success, name: '新增用户' },
              { dataKey: 'access', color: CHART_COLORS.warning, name: '访问量' }
            ]}
            xAxisKey="date"
            height={300}
            formatXAxis={formatDate}
          />
        </CardContent>
      </Card>
    </div>
  );
}

// 用户分析标签页
function UsersTab({ userAnalytics }: { userAnalytics: UserAnalytics }) {
  return (
    <div className="space-y-6">
      {/* 活跃用户统计 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-xs text-gray-600">日活跃用户</p>
              <p className="text-2xl font-bold text-blue-600">{userAnalytics.activeUsers.daily}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-xs text-gray-600">周活跃用户</p>
              <p className="text-2xl font-bold text-green-600">{userAnalytics.activeUsers.weekly}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-xs text-gray-600">月活跃用户</p>
              <p className="text-2xl font-bold text-purple-600">{userAnalytics.activeUsers.monthly}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 用户等级分布 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">用户等级分布</CardTitle>
          </CardHeader>
          <CardContent>
            <PieChart
              data={userAnalytics.levelDistribution.map(item => ({
                name: item.level.toUpperCase(),
                value: item.count
              }))}
              height={250}
              formatTooltip={(value, name) => [value.toString(), name]}
            />
          </CardContent>
        </Card>

        {/* 用户活动类型 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">用户活动类型</CardTitle>
          </CardHeader>
          <CardContent>
            <BarChart
              data={userAnalytics.activityTrends}
              dataKey="count"
              xAxisKey="type"
              color={CHART_COLORS.success}
              height={250}
              formatTooltip={(value, name) => [value.toString(), '活动次数']}
            />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 地理分布 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">地理位置分布</CardTitle>
          </CardHeader>
          <CardContent>
            <BarChart
              data={userAnalytics.geoDistribution.slice(0, 10)}
              dataKey="count"
              xAxisKey="location"
              color={CHART_COLORS.cyan}
              height={250}
              horizontal={true}
              formatTooltip={(value, name) => [value.toString(), '用户数']}
            />
          </CardContent>
        </Card>

        {/* 设备统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">设备类型分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <PieChart
                data={[
                  { name: '移动设备', value: userAnalytics.deviceStats.mobile },
                  { name: '桌面设备', value: userAnalytics.deviceStats.desktop }
                ]}
                height={200}
                formatTooltip={(value, name) => [value.toString(), name]}
              />

              <div className="mt-4">
                <h4 className="text-xs font-medium text-gray-700 mb-2">浏览器分布</h4>
                <div className="space-y-2">
                  {Object.entries(userAnalytics.deviceStats.browsers)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 5)
                    .map(([browser, count]) => (
                      <div key={browser} className="flex items-center justify-between text-xs">
                        <span className="text-gray-600">{browser}</span>
                        <Badge variant="secondary">{count}</Badge>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// 上传分析标签页
function UploadsTab({ uploadAnalytics }: { uploadAnalytics: UploadAnalytics }) {
  return (
    <div className="space-y-6">
      {/* 上传成功率 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">上传成功率</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-xs text-gray-600">总上传</p>
              <p className="text-xl font-bold text-gray-900">{uploadAnalytics.successRate.total}</p>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-600">成功</p>
              <p className="text-xl font-bold text-green-600">{uploadAnalytics.successRate.successful}</p>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-600">失败</p>
              <p className="text-xl font-bold text-red-600">{uploadAnalytics.successRate.failed}</p>
            </div>
            <div className="text-center">
              <p className="text-xs text-gray-600">成功率</p>
              <p className="text-xl font-bold text-blue-600">{uploadAnalytics.successRate.successRate.toFixed(1)}%</p>
            </div>
          </div>

          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadAnalytics.successRate.successRate}%` }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 上传趋势 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">上传趋势</CardTitle>
          </CardHeader>
          <CardContent>
            <AreaChart
              data={uploadAnalytics.trends}
              dataKey="count"
              xAxisKey="date"
              color={CHART_COLORS.primary}
              height={250}
              formatXAxis={formatDate}
              formatTooltip={(value, name) => [value.toString(), '上传量']}
            />
          </CardContent>
        </Card>

        {/* 文件类型分布 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">文件类型分布</CardTitle>
          </CardHeader>
          <CardContent>
            <PieChart
              data={uploadAnalytics.fileTypes.map(item => ({
                name: item.type.split('/')[1] || item.type,
                value: item.count
              }))}
              height={250}
              formatTooltip={(value, name) => [value.toString(), name]}
            />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 文件大小分布 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">文件大小分布</CardTitle>
          </CardHeader>
          <CardContent>
            <BarChart
              data={uploadAnalytics.fileSizes.map(item => ({
                range: item.range === 'small' ? '<1MB' :
                       item.range === 'medium' ? '1-5MB' :
                       item.range === 'large' ? '5-10MB' : '>10MB',
                count: item.count
              }))}
              dataKey="count"
              xAxisKey="range"
              color={CHART_COLORS.warning}
              height={250}
              formatTooltip={(value, name) => [value.toString(), '文件数']}
            />
          </CardContent>
        </Card>

        {/* 热门上传者 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">热门上传者</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {uploadAnalytics.topUploaders.slice(0, 8).map((uploader, index) => (
                <div key={uploader.userId} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                      {index + 1}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{uploader.username}</p>
                      <p className="text-xs text-gray-500">
                        <Badge variant="secondary" size="sm">{uploader.userLevel.toUpperCase()}</Badge>
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{uploader.uploadCount}</p>
                    <p className="text-xs text-gray-500">上传</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 热门图片 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">热门图片</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-xs">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2 px-3 font-medium text-gray-700">文件名</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">上传者</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">大小</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">访问次数</th>
                  <th className="text-left py-2 px-3 font-medium text-gray-700">类型</th>
                </tr>
              </thead>
              <tbody>
                {uploadAnalytics.popularImages.slice(0, 10).map((image) => (
                  <tr key={image.imageId} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-2 px-3">
                      <p className="font-medium text-gray-900 truncate max-w-32" title={image.fileName}>
                        {image.fileName}
                      </p>
                    </td>
                    <td className="py-2 px-3">
                      <div>
                        <p className="text-gray-900">{image.uploader.username}</p>
                        <Badge variant="secondary" size="sm">{image.uploader.level.toUpperCase()}</Badge>
                      </div>
                    </td>
                    <td className="py-2 px-3 text-gray-600">
                      {formatFileSize(image.fileSize)}
                    </td>
                    <td className="py-2 px-3">
                      <Badge variant="outline">{image.totalAccess}</Badge>
                    </td>
                    <td className="py-2 px-3 text-gray-600">
                      {image.mimeType.split('/')[1]}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
