/**
 * 测试邀请码生成功能
 */

const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

// 邀请码字符集：大小写字母 + 数字，排除容易混淆的字符
const CHARSET = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789abcdefghjkmnpqrstuvwxyz';
const CODE_LENGTH = 13;

function generateRandomCode() {
  let code = '';
  const charsetLength = CHARSET.length;
  
  for (let i = 0; i < CODE_LENGTH; i++) {
    const randomIndex = crypto.randomInt(0, charsetLength);
    code += CHARSET[randomIndex];
  }
  
  return code;
}

async function generateUniqueCode() {
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    const code = generateRandomCode();
    
    // 检查是否已存在
    const existing = await prisma.invitationCode.findUnique({
      where: { code }
    });

    if (!existing) {
      return code;
    }

    attempts++;
  }

  throw new Error('生成唯一邀请码失败，请重试');
}

async function testGenerateCode() {
  try {
    console.log('🧪 开始测试邀请码生成功能...\n');

    // 1. 测试生成随机码
    console.log('📝 测试生成随机码...');
    const randomCode = generateRandomCode();
    console.log(`✅ 生成随机码: ${randomCode} (长度: ${randomCode.length})`);

    // 2. 测试生成唯一码
    console.log('\n📝 测试生成唯一码...');
    const uniqueCode = await generateUniqueCode();
    console.log(`✅ 生成唯一码: ${uniqueCode}`);

    // 3. 测试创建邀请码记录
    console.log('\n📝 测试创建邀请码记录...');
    
    // 首先检查是否有管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.log('❌ 没有找到管理员用户，无法测试创建邀请码记录');
      return;
    }

    console.log(`📋 使用管理员用户: ${adminUser.username} (ID: ${adminUser.id})`);

    const invitationCode = await prisma.invitationCode.create({
      data: {
        code: uniqueCode,
        createdBy: adminUser.id,
        description: '测试邀请码',
        expiresAt: null
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    console.log('✅ 邀请码创建成功:');
    console.log(`   ID: ${invitationCode.id}`);
    console.log(`   Code: ${invitationCode.code}`);
    console.log(`   Creator: ${invitationCode.creator.username}`);
    console.log(`   Created At: ${invitationCode.createdAt}`);

    // 4. 清理测试数据
    console.log('\n🧹 清理测试数据...');
    await prisma.invitationCode.delete({
      where: { id: invitationCode.id }
    });
    console.log('✅ 测试数据清理完成');

    console.log('\n🎉 邀请码生成功能测试通过！');

  } catch (error) {
    console.error('❌ 邀请码生成功能测试失败:', error);
    console.error('错误详情:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testGenerateCode().catch(error => {
  console.error('❌ 测试脚本执行失败:', error);
  process.exit(1);
});
