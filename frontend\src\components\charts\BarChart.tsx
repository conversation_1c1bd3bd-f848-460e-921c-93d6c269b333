import React from 'react';
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ar<PERSON>hart,
  Bar,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';

interface DataPoint {
  [key: string]: any;
}

interface BarChartProps {
  data: DataPoint[];
  dataKey: string;
  xAxisKey: string;
  title?: string;
  color?: string;
  height?: number;
  showGrid?: boolean;
  showTooltip?: boolean;
  formatXAxis?: (value: any) => string;
  formatTooltip?: (value: any, name: string) => [string, string];
  className?: string;
  horizontal?: boolean;
}

const CustomTooltip: React.FC<any> = ({ active, payload, label, formatTooltip }) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    const [formattedValue, formattedName] = formatTooltip 
      ? formatTooltip(data.value, data.name)
      : [data.value, data.name];

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm text-gray-600 mb-1">{label}</p>
        <p className="text-sm font-medium" style={{ color: data.color }}>
          {formattedName}: {formattedValue}
        </p>
      </div>
    );
  }
  return null;
};

export function BarChart({
  data,
  dataKey,
  xAxisKey,
  title,
  color = '#10b981',
  height = 300,
  showGrid = true,
  showTooltip = true,
  formatXAxis,
  formatTooltip,
  className = '',
  horizontal = false
}: BarChartProps) {
  return (
    <div className={`w-full ${className}`}>
      {title && (
        <h3 className="text-sm font-medium text-gray-900 mb-4">{title}</h3>
      )}
      <div style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <RechartsBarChart 
            data={data} 
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            layout={horizontal ? 'horizontal' : 'vertical'}
          >
            {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
            <XAxis
              type={horizontal ? 'number' : 'category'}
              dataKey={horizontal ? undefined : xAxisKey}
              tickFormatter={formatXAxis}
              fontSize={12}
              stroke="#6b7280"
              axisLine={false}
              tickLine={false}
            />
            <YAxis
              type={horizontal ? 'category' : 'number'}
              dataKey={horizontal ? xAxisKey : undefined}
              fontSize={12}
              stroke="#6b7280"
              axisLine={false}
              tickLine={false}
            />
            {showTooltip && (
              <Tooltip
                content={<CustomTooltip formatTooltip={formatTooltip} />}
              />
            )}
            <Bar
              dataKey={dataKey}
              fill={color}
              radius={[2, 2, 0, 0]}
            />
          </RechartsBarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
