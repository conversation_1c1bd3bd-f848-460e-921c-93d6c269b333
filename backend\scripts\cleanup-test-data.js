/**
 * 清理测试数据脚本
 * 删除之前创建的测试数据，保留真实数据
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function cleanupTestData() {
  try {
    console.log('🧹 开始清理测试数据...\n');

    // 清理测试用户活动日志（基于特定标识）
    const testActivityLogs = await prisma.userActivityLog.deleteMany({
      where: {
        activityData: {
          path: ['test'],
          equals: true
        }
      }
    });
    console.log(`✅ 清理测试用户活动日志: ${testActivityLogs.count} 条`);

    // 清理测试上传日志（基于文件名模式）
    const testUploadLogs = await prisma.uploadLog.deleteMany({
      where: {
        fileName: {
          startsWith: 'test-image-'
        }
      }
    });
    console.log(`✅ 清理测试上传日志: ${testUploadLogs.count} 条`);

    // 清理测试图片记录（基于文件名模式）
    const testImages = await prisma.image.deleteMany({
      where: {
        originalName: {
          startsWith: 'test-image-'
        }
      }
    });
    console.log(`✅ 清理测试图片记录: ${testImages.count} 条`);

    // 清理测试用户图片关联（如果有的话）
    const testUserImages = await prisma.userImage.deleteMany({
      where: {
        image: {
          originalName: {
            startsWith: 'test-image-'
          }
        }
      }
    });
    console.log(`✅ 清理测试用户图片关联: ${testUserImages.count} 条`);

    console.log('\n🎉 测试数据清理完成！');
    
    // 重新检查数据状态
    console.log('\n📊 清理后的数据统计:');
    
    const userCount = await prisma.user.count();
    const imageCount = await prisma.image.count();
    const userImageCount = await prisma.userImage.count();
    const uploadLogCount = await prisma.uploadLog.count();
    const accessLogCount = await prisma.accessLog.count();
    const activityLogCount = await prisma.userActivityLog.count();
    const systemMetricCount = await prisma.systemMetric.count();

    console.log(`   👥 用户数: ${userCount}`);
    console.log(`   📷 图片数: ${imageCount}`);
    console.log(`   🔗 用户图片关联: ${userImageCount}`);
    console.log(`   📤 上传日志: ${uploadLogCount}`);
    console.log(`   👁️ 访问日志: ${accessLogCount}`);
    console.log(`   📊 用户活动日志: ${activityLogCount}`);
    console.log(`   📈 系统指标: ${systemMetricCount}`);

    console.log('\n✨ 现在数据分析功能将只使用真实的系统数据！');

  } catch (error) {
    console.error('❌ 清理测试数据失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanupTestData().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { cleanupTestData };
