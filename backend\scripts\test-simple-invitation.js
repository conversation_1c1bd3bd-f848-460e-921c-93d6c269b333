/**
 * 简单测试邀请码生成
 */

const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

// 邀请码字符集：大小写字母 + 数字，排除容易混淆的字符
const CHARSET = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789abcdefghjkmnpqrstuvwxyz';
const CODE_LENGTH = 13;

function generateRandomCode() {
  let code = '';
  const charsetLength = CHARSET.length;
  
  for (let i = 0; i < CODE_LENGTH; i++) {
    const randomIndex = crypto.randomInt(0, charsetLength);
    code += CHARSET[randomIndex];
  }
  
  return code;
}

async function testSimpleInvitation() {
  try {
    console.log('🧪 简单测试邀请码生成...\n');

    // 1. 生成随机码
    const code = generateRandomCode();
    console.log(`📝 生成的邀请码: ${code}`);

    // 2. 查找管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' }
    });

    if (!adminUser) {
      console.log('❌ 没有找到管理员用户');
      return;
    }

    console.log(`👤 找到管理员: ${adminUser.username} (ID: ${adminUser.id})`);

    // 3. 直接创建邀请码记录
    console.log('\n📝 创建邀请码记录...');
    
    const invitationCode = await prisma.invitationCode.create({
      data: {
        code: code,
        createdBy: adminUser.id,
        description: '测试邀请码 - 直接创建',
        expiresAt: null, // 永不过期
        isUsed: false,
        usedAt: null,
        usedBy: null
      }
    });

    console.log('✅ 邀请码创建成功:');
    console.log(`   ID: ${invitationCode.id}`);
    console.log(`   Code: ${invitationCode.code}`);
    console.log(`   Created By: ${invitationCode.createdBy}`);
    console.log(`   Created At: ${invitationCode.createdAt}`);
    console.log(`   Is Used: ${invitationCode.isUsed}`);

    // 4. 验证邀请码是否可以查询
    console.log('\n🔍 验证邀请码查询...');
    const foundCode = await prisma.invitationCode.findUnique({
      where: { code: code }
    });

    if (foundCode) {
      console.log('✅ 邀请码查询成功');
    } else {
      console.log('❌ 邀请码查询失败');
    }

    // 5. 清理测试数据
    console.log('\n🧹 清理测试数据...');
    await prisma.invitationCode.delete({
      where: { id: invitationCode.id }
    });
    console.log('✅ 测试数据清理完成');

    console.log('\n🎉 简单邀请码测试通过！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testSimpleInvitation();
