import React, { useState, useEffect } from 'react';
import { UserProviderService, UserProvider, UserProviderStats } from '../services/user-provider.service';
import { getCurrentBackendPort } from '../config/api.config';

const UserProviderTest: React.FC = () => {
  const [providers, setProviders] = useState<UserProvider[]>([]);
  const [stats, setStats] = useState<UserProviderStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userId, setUserId] = useState<number>(1);
  const [currentBackendPort, setCurrentBackendPort] = useState<number>(getCurrentBackendPort());

  // 监听后端端口变化
  useEffect(() => {
    const handlePortChange = (event: CustomEvent) => {
      setCurrentBackendPort(event.detail.newPort);
      console.log(`🔄 前端检测到后端端口变化: ${event.detail.oldPort} -> ${event.detail.newPort}`);
      // 重新加载数据
      loadData();
    };

    window.addEventListener('backend-port-changed', handlePortChange as EventListener);
    
    return () => {
      window.removeEventListener('backend-port-changed', handlePortChange as EventListener);
    };
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 并行加载用户可用接口和统计数据
      const [providersData, statsData] = await Promise.all([
        UserProviderService.getAvailableProviders(userId),
        UserProviderService.getUserProviderStats(userId, 30)
      ]);
      
      setProviders(providersData);
      setStats(statsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载数据失败');
      console.error('加载数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleInitializeVisibility = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await UserProviderService.initializeLevelProviderVisibility();
      alert('初始化等级接口可见性配置成功！');
      await loadData(); // 重新加载数据
    } catch (err) {
      setError(err instanceof Error ? err.message : '初始化配置失败');
      console.error('初始化配置失败:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [userId]);

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">用户接口权限管理测试</h1>
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span>当前后端端口: <strong className="text-blue-600">{currentBackendPort}</strong></span>
          <span>前端端口: <strong className="text-green-600">5174</strong></span>
        </div>
      </div>

      {/* 用户ID输入 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          测试用户ID:
        </label>
        <div className="flex items-center gap-4">
          <input
            type="number"
            value={userId}
            onChange={(e) => setUserId(parseInt(e.target.value) || 1)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            min="1"
          />
          <button
            onClick={loadData}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? '加载中...' : '重新加载'}
          </button>
          <button
            onClick={handleInitializeVisibility}
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            初始化配置
          </button>
        </div>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 可用接口列表 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            可用接口列表 ({providers.length})
          </h2>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : providers.length === 0 ? (
            <p className="text-gray-500 text-center py-8">暂无可用接口</p>
          ) : (
            <div className="space-y-3">
              {providers.map((provider) => (
                <div
                  key={provider.id}
                  className={`p-4 rounded-lg border-2 ${
                    provider.isAvailable
                      ? 'border-green-200 bg-green-50'
                      : 'border-gray-200 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900">{provider.name}</h3>
                    <div className="flex items-center gap-2">
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${
                          provider.source === 'manual'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}
                      >
                        {provider.source === 'manual' ? '手动授权' : '等级权限'}
                      </span>
                      {provider.isPremium && (
                        <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                          高级
                        </span>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{provider.description}</p>
                  <div className="text-xs text-gray-500 space-y-1">
                    <div>优先级: {provider.priority}</div>
                    <div>最大文件大小: {(provider.maxFileSize / 1024 / 1024).toFixed(1)}MB</div>
                    <div>所需等级: {provider.requiredLevel}</div>
                    {provider.costPerUpload > 0 && (
                      <div>每次上传费用: {provider.costPerUpload}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 使用统计 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            使用统计 (最近30天)
          </h2>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : stats.length === 0 ? (
            <p className="text-gray-500 text-center py-8">暂无使用记录</p>
          ) : (
            <div className="space-y-3">
              {stats.map((stat, index) => (
                <div key={index} className="p-4 rounded-lg border border-gray-200">
                  <h3 className="font-medium text-gray-900 mb-2">{stat.providerName}</h3>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">上传次数:</span>
                      <span className="ml-2 font-medium">{stat.uploadCount}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">平均响应时间:</span>
                      <span className="ml-2 font-medium">{stat.avgResponseTime.toFixed(2)}ms</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserProviderTest;
