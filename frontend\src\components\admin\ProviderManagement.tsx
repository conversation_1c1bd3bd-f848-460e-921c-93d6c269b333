import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { adminService } from '../../services/admin.service';
import { Provider } from '../../types';

export function ProviderManagement() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingProvider, setEditingProvider] = useState<Provider | null>(null);
  const [showMessage, setShowMessage] = useState(false);
  const [message, setMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // 表单数据状态
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    endpoint: '',
    apiKey: '',
    config: {},
    priority: 1,
    maxFileSize: 10485760, // 10MB
    supportedFormats: ['image/jpeg', 'image/png', 'image/gif'],
    requiredLevel: 'free',
    isPremium: false,
    costPerUpload: 0
  });

  const [formErrors, setFormErrors] = useState({
    name: '',
    endpoint: ''
  });

  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    try {
      setLoading(true);
      const params: any = {};

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }

      const data = await adminService.getProviders(params);
      setProviders(data.providers);
    } catch (error) {
      console.error('加载接口列表失败:', error);
      showSuccessMessage('加载接口列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const showSuccessMessage = (msg: string) => {
    setMessage(msg);
    setShowMessage(true);
    setTimeout(() => {
      setShowMessage(false);
      setMessage('');
    }, 3000);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      endpoint: '',
      apiKey: '',
      config: {},
      priority: 1,
      maxFileSize: 10485760,
      supportedFormats: ['image/jpeg', 'image/png', 'image/gif'],
      requiredLevel: 'free',
      isPremium: false,
      costPerUpload: 0
    });
    setFormErrors({
      name: '',
      endpoint: ''
    });
  };

  const validateForm = (): boolean => {
    const errors = { name: '', endpoint: '' };
    let isValid = true;

    if (!formData.name.trim()) {
      errors.name = '接口名称不能为空';
      isValid = false;
    }

    if (!formData.endpoint.trim()) {
      errors.endpoint = '接口地址不能为空';
      isValid = false;
    } else {
      try {
        new URL(formData.endpoint);
      } catch {
        errors.endpoint = '请输入有效的URL地址';
        isValid = false;
      }
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleAddProvider = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      await adminService.createProvider(formData);
      await loadProviders();
      setShowAddDialog(false);
      resetForm();
      showSuccessMessage('接口添加成功');
    } catch (error) {
      console.error('添加接口失败:', error);
      showSuccessMessage('添加接口失败，请稍后重试');
    }
  };

  const handleEditProvider = async () => {
    if (!validateForm() || !editingProvider) {
      return;
    }

    try {
      await adminService.updateProvider(editingProvider.id, formData);
      await loadProviders();
      setShowEditDialog(false);
      setEditingProvider(null);
      resetForm();
      showSuccessMessage('接口更新成功');
    } catch (error) {
      console.error('更新接口失败:', error);
      showSuccessMessage('更新接口失败，请稍后重试');
    }
  };

  const handleDeleteProvider = async (provider: Provider) => {
    if (!confirm(`确定要删除接口"${provider.name}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      await adminService.deleteProvider(provider.id);
      await loadProviders();
      showSuccessMessage('接口删除成功');
    } catch (error) {
      console.error('删除接口失败:', error);
      showSuccessMessage('删除接口失败，请稍后重试');
    }
  };

  const handleTestProvider = async (provider: Provider) => {
    try {
      const result = await adminService.testProvider(provider.id);
      showSuccessMessage(result.message);
    } catch (error) {
      console.error('测试接口失败:', error);
      showSuccessMessage('测试接口失败，请稍后重试');
    }
  };

  const openEditDialog = (provider: Provider) => {
    setEditingProvider(provider);
    setFormData({
      name: provider.name,
      description: provider.description || '',
      endpoint: provider.endpoint,
      apiKey: provider.apiKey || '',
      config: provider.config || {},
      priority: provider.priority,
      maxFileSize: provider.maxFileSize,
      supportedFormats: provider.supportedFormats,
      requiredLevel: provider.requiredLevel,
      isPremium: provider.isPremium,
      costPerUpload: provider.costPerUpload
    });
    setShowEditDialog(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="success">启用</Badge>;
      case 'inactive':
        return <Badge variant="secondary">禁用</Badge>;
      case 'error':
        return <Badge variant="destructive">错误</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* 头部操作区 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>接口管理</CardTitle>
            <Button
              onClick={() => {
                resetForm();
                setShowAddDialog(true);
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              ➕ 添加接口
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            {/* 搜索框 */}
            <div className="flex-1 min-w-64">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="搜索接口名称或描述..."
              />
            </div>

            {/* 状态筛选 */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">全部状态</option>
              <option value="active">启用</option>
              <option value="inactive">禁用</option>
              <option value="error">错误</option>
            </select>

            {/* 搜索按钮 */}
            <Button
              onClick={loadProviders}
              variant="outline"
            >
              🔍 搜索
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 接口列表 */}
      <Card>
        <CardHeader>
          <CardTitle>接口列表</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="loading-spinner w-6 h-6 mx-auto mb-2" />
              <p className="text-gray-600">加载中...</p>
            </div>
          ) : providers.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">📡</div>
              <p>暂无接口配置</p>
              <p className="text-sm mt-1">点击"添加接口"开始配置第三方上传接口</p>
            </div>
          ) : (
            <div className="space-y-4">
              {providers.map((provider) => (
                <div key={provider.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">{provider.name}</h3>
                        {getStatusBadge(provider.status)}
                        {provider.isPremium && (
                          <Badge variant="warning">高级</Badge>
                        )}
                        <span className="text-sm text-gray-500">优先级: {provider.priority}</span>
                      </div>

                      {provider.description && (
                        <p className="text-gray-600 mb-2">{provider.description}</p>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-gray-700">接口地址:</span>
                          <p className="text-gray-600 break-all">{provider.endpoint}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">最大文件:</span>
                          <p className="text-gray-600">{formatFileSize(provider.maxFileSize)}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">用户等级:</span>
                          <p className="text-gray-600">{provider.requiredLevel}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">支持格式:</span>
                          <p className="text-gray-600">{provider.supportedFormats.join(', ')}</p>
                        </div>
                      </div>

                      {provider.costPerUpload > 0 && (
                        <div className="mt-2">
                          <span className="text-sm font-medium text-gray-700">上传成本: </span>
                          <span className="text-sm text-gray-600">${provider.costPerUpload.toFixed(4)}/次</span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        onClick={() => handleTestProvider(provider)}
                        size="sm"
                        variant="outline"
                        className="text-blue-600 hover:text-blue-700"
                      >
                        🔍 测试
                      </Button>
                      <Button
                        onClick={() => openEditDialog(provider)}
                        size="sm"
                        variant="outline"
                        className="text-green-600 hover:text-green-700"
                      >
                        ✏️ 编辑
                      </Button>
                      <Button
                        onClick={() => handleDeleteProvider(provider)}
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700"
                      >
                        🗑️ 删除
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加接口对话框 */}
      {showAddDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 shadow-lg max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4 text-gray-900">添加接口</h3>

            <div className="space-y-4">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    接口名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, name: e.target.value }));
                      if (formErrors.name) {
                        setFormErrors(prev => ({ ...prev, name: '' }));
                      }
                    }}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      formErrors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="如：七牛云、Imgur等"
                  />
                  {formErrors.name && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    优先级
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) || 1 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">数字越小优先级越高</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  接口描述
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  rows={2}
                  placeholder="接口的详细描述..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  接口地址 <span className="text-red-500">*</span>
                </label>
                <input
                  type="url"
                  value={formData.endpoint}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, endpoint: e.target.value }));
                    if (formErrors.endpoint) {
                      setFormErrors(prev => ({ ...prev, endpoint: '' }));
                    }
                  }}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    formErrors.endpoint ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="https://api.example.com/upload"
                />
                {formErrors.endpoint && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.endpoint}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  API密钥
                </label>
                <input
                  type="password"
                  value={formData.apiKey}
                  onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="接口的API密钥（可选）"
                />
              </div>

              {/* 配置选项 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最大文件大小 (字节)
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={formData.maxFileSize}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxFileSize: parseInt(e.target.value) || 10485760 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">当前: {formatFileSize(formData.maxFileSize)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    所需用户等级
                  </label>
                  <select
                    value={formData.requiredLevel}
                    onChange={(e) => setFormData(prev => ({ ...prev, requiredLevel: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="free">免费用户</option>
                    <option value="vip1">VIP1</option>
                    <option value="vip2">VIP2</option>
                    <option value="vip3">VIP3</option>
                    <option value="admin">管理员</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isPremium}
                    onChange={(e) => setFormData(prev => ({ ...prev, isPremium: e.target.checked }))}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">高级接口</span>
                </label>

                {formData.isPremium && (
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-700">上传成本:</label>
                    <input
                      type="number"
                      min="0"
                      step="0.0001"
                      value={formData.costPerUpload}
                      onChange={(e) => setFormData(prev => ({ ...prev, costPerUpload: parseFloat(e.target.value) || 0 }))}
                      className="w-24 px-2 py-1 border border-gray-300 rounded text-sm"
                    />
                    <span className="text-sm text-gray-500">$/次</span>
                  </div>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3 mt-6">
              <Button
                onClick={() => {
                  setShowAddDialog(false);
                  resetForm();
                }}
                variant="outline"
              >
                取消
              </Button>
              <Button
                onClick={handleAddProvider}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                添加接口
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑接口对话框 */}
      {showEditDialog && editingProvider && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 shadow-lg max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4 text-gray-900">编辑接口 - {editingProvider.name}</h3>

            <div className="space-y-4">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    接口名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, name: e.target.value }));
                      if (formErrors.name) {
                        setFormErrors(prev => ({ ...prev, name: '' }));
                      }
                    }}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      formErrors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="如：七牛云、Imgur等"
                  />
                  {formErrors.name && (
                    <p className="text-red-500 text-xs mt-1">{formErrors.name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    优先级
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) || 1 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">数字越小优先级越高</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  接口状态
                </label>
                <select
                  value={formData.status || editingProvider.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="active">启用</option>
                  <option value="inactive">禁用</option>
                  <option value="error">错误</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  接口描述
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  rows={2}
                  placeholder="接口的详细描述..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  接口地址 <span className="text-red-500">*</span>
                </label>
                <input
                  type="url"
                  value={formData.endpoint}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, endpoint: e.target.value }));
                    if (formErrors.endpoint) {
                      setFormErrors(prev => ({ ...prev, endpoint: '' }));
                    }
                  }}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    formErrors.endpoint ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="https://api.example.com/upload"
                />
                {formErrors.endpoint && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.endpoint}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  API密钥
                </label>
                <input
                  type="password"
                  value={formData.apiKey}
                  onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="留空表示不修改现有密钥"
                />
              </div>

              {/* 配置选项 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最大文件大小 (字节)
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={formData.maxFileSize}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxFileSize: parseInt(e.target.value) || 10485760 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">当前: {formatFileSize(formData.maxFileSize)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    所需用户等级
                  </label>
                  <select
                    value={formData.requiredLevel}
                    onChange={(e) => setFormData(prev => ({ ...prev, requiredLevel: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="free">免费用户</option>
                    <option value="vip1">VIP1</option>
                    <option value="vip2">VIP2</option>
                    <option value="vip3">VIP3</option>
                    <option value="admin">管理员</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isPremium}
                    onChange={(e) => setFormData(prev => ({ ...prev, isPremium: e.target.checked }))}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">高级接口</span>
                </label>

                {formData.isPremium && (
                  <div className="flex items-center space-x-2">
                    <label className="text-sm text-gray-700">上传成本:</label>
                    <input
                      type="number"
                      min="0"
                      step="0.0001"
                      value={formData.costPerUpload}
                      onChange={(e) => setFormData(prev => ({ ...prev, costPerUpload: parseFloat(e.target.value) || 0 }))}
                      className="w-24 px-2 py-1 border border-gray-300 rounded text-sm"
                    />
                    <span className="text-sm text-gray-500">$/次</span>
                  </div>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3 mt-6">
              <Button
                onClick={() => {
                  setShowEditDialog(false);
                  setEditingProvider(null);
                  resetForm();
                }}
                variant="outline"
              >
                取消
              </Button>
              <Button
                onClick={handleEditProvider}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                保存修改
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 消息提示弹窗 */}
      {showMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-sm mx-4 shadow-lg">
            <div className="text-center">
              <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4 ${
                message.includes('失败') ? 'bg-red-100' : 'bg-green-100'
              }`}>
                {message.includes('失败') ? (
                  <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {message.includes('失败') ? '操作失败' : '操作成功'}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {message}
              </p>
              <Button
                onClick={() => {
                  setShowMessage(false);
                  setMessage('');
                }}
                className="w-full"
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
