"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityService = void 0;
const database_1 = require("../config/database");
class SecurityService {
    async getIPSecurityOverview() {
        const [totalIPs, blockedIPs, riskIPs, recentActivity, activeRules] = await Promise.all([
            this.getTotalUniqueIPs(),
            this.getBlockedIPsCount(),
            this.getRiskIPsCount(),
            this.getRecentIPActivity(),
            this.getActiveRiskRules()
        ]);
        return {
            summary: {
                totalIPs,
                blockedIPs,
                riskIPs,
                activeRules: activeRules.length
            },
            recentActivity,
            activeRules: activeRules.slice(0, 5)
        };
    }
    async getIPRiskLogs(options = {}) {
        const { page = 1, limit = 20, ipAddress, actionType, isBlocked, startDate, endDate } = options;
        const where = {};
        if (ipAddress) {
            where.ipAddress = { contains: ipAddress };
        }
        if (actionType) {
            where.actionType = actionType;
        }
        if (typeof isBlocked === 'boolean') {
            where.isBlocked = isBlocked;
        }
        if (startDate || endDate) {
            where.createdAt = {};
            if (startDate)
                where.createdAt.gte = startDate;
            if (endDate)
                where.createdAt.lte = endDate;
        }
        const [logs, total] = await Promise.all([
            database_1.prisma.ipRiskLog.findMany({
                where,
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            userLevel: true
                        }
                    },
                    rule: {
                        select: {
                            id: true,
                            ruleName: true,
                            ruleType: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' },
                skip: (page - 1) * limit,
                take: limit
            }),
            database_1.prisma.ipRiskLog.count({ where })
        ]);
        return {
            logs,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };
    }
    async getIPRiskRules() {
        const rules = await database_1.prisma.ipRiskRule.findMany({
            orderBy: { createdAt: 'desc' },
            include: {
                _count: {
                    select: {
                        ipRiskLogs: {
                            where: {
                                createdAt: {
                                    gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                                }
                            }
                        }
                    }
                }
            }
        });
        return rules.map(rule => ({
            ...rule,
            recentTriggers: rule._count.ipRiskLogs
        }));
    }
    async createIPRiskRule(data) {
        return await database_1.prisma.ipRiskRule.create({
            data: {
                ...data,
                isActive: data.isActive ?? true
            }
        });
    }
    async updateIPRiskRule(id, data) {
        return await database_1.prisma.ipRiskRule.update({
            where: { id },
            data
        });
    }
    async deleteIPRiskRule(id) {
        return await database_1.prisma.ipRiskRule.delete({
            where: { id }
        });
    }
    async blockIP(data) {
        const blockExpiresAt = new Date();
        blockExpiresAt.setMinutes(blockExpiresAt.getMinutes() + data.blockDuration);
        return await database_1.prisma.ipRiskLog.create({
            data: {
                ipAddress: data.ipAddress,
                actionType: 'manual_block',
                isBlocked: true,
                blockExpiresAt,
                riskScore: 100,
                userAgent: `Manual block by admin ${data.adminId}: ${data.reason}`
            }
        });
    }
    async unblockIP(ipAddress, adminId) {
        await database_1.prisma.ipRiskLog.updateMany({
            where: {
                ipAddress,
                isBlocked: true,
                blockExpiresAt: {
                    gt: new Date()
                }
            },
            data: {
                blockExpiresAt: new Date()
            }
        });
        return await database_1.prisma.ipRiskLog.create({
            data: {
                ipAddress,
                actionType: 'manual_unblock',
                isBlocked: false,
                riskScore: 0,
                userAgent: `Manual unblock by admin ${adminId}`
            }
        });
    }
    async getUserPermissionsOverview() {
        const [totalUsers, adminUsers, vipUsers, bannedUsers, recentPermissionChanges] = await Promise.all([
            database_1.prisma.user.count(),
            database_1.prisma.user.count({ where: { role: 'admin' } }),
            database_1.prisma.user.count({ where: { userLevel: { not: 'free' } } }),
            database_1.prisma.user.count({ where: { status: 'banned' } }),
            this.getRecentPermissionChanges()
        ]);
        return {
            summary: {
                totalUsers,
                adminUsers,
                vipUsers,
                bannedUsers
            },
            recentChanges: recentPermissionChanges
        };
    }
    async getUserSessions(options = {}) {
        const { page = 1, limit = 20, userId, ipAddress, isActive } = options;
        const where = {};
        if (userId) {
            where.userId = userId;
        }
        if (ipAddress) {
            where.ipAddress = { contains: ipAddress };
        }
        if (typeof isActive === 'boolean') {
            if (isActive) {
                where.expiresAt = { gt: new Date() };
            }
            else {
                where.expiresAt = { lte: new Date() };
            }
        }
        const [sessions, total] = await Promise.all([
            database_1.prisma.userSession.findMany({
                where,
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            userLevel: true,
                            role: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' },
                skip: (page - 1) * limit,
                take: limit
            }),
            database_1.prisma.userSession.count({ where })
        ]);
        return {
            sessions: sessions.map(session => ({
                ...session,
                isActive: session.expiresAt > new Date()
            })),
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };
    }
    async revokeUserSession(sessionId, adminId) {
        const session = await database_1.prisma.userSession.update({
            where: { id: sessionId },
            data: {
                expiresAt: new Date()
            },
            include: {
                user: {
                    select: { username: true }
                }
            }
        });
        await this.logSecurityAction(adminId, 'revoke_session', {
            sessionId,
            targetUser: session.user.username,
            ipAddress: session.ipAddress
        });
        return session;
    }
    async getSecurityConfigs() {
        return await database_1.prisma.securityConfig.findMany({
            orderBy: { configKey: 'asc' }
        });
    }
    async updateSecurityConfig(configKey, configValue, adminId) {
        const config = await database_1.prisma.securityConfig.upsert({
            where: { configKey },
            update: {
                configValue,
                updatedBy: adminId,
                updatedAt: new Date()
            },
            create: {
                configKey,
                configValue,
                updatedBy: adminId
            }
        });
        await this.logSecurityAction(adminId, 'update_security_config', {
            configKey,
            configValue
        });
        return config;
    }
    async getTotalUniqueIPs() {
        const result = await database_1.prisma.ipRiskLog.groupBy({
            by: ['ipAddress'],
            _count: true
        });
        return result.length;
    }
    async getBlockedIPsCount() {
        const result = await database_1.prisma.ipRiskLog.groupBy({
            by: ['ipAddress'],
            where: {
                isBlocked: true,
                blockExpiresAt: { gt: new Date() }
            }
        });
        return result.length;
    }
    async getRiskIPsCount() {
        const result = await database_1.prisma.ipRiskLog.groupBy({
            by: ['ipAddress'],
            where: {
                riskScore: { gte: 50 },
                createdAt: {
                    gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
                }
            }
        });
        return result.length;
    }
    async getRecentIPActivity() {
        return await database_1.prisma.ipRiskLog.findMany({
            take: 10,
            orderBy: { createdAt: 'desc' },
            include: {
                user: {
                    select: { username: true }
                }
            }
        });
    }
    async getActiveRiskRules() {
        return await database_1.prisma.ipRiskRule.findMany({
            where: { isActive: true },
            orderBy: { createdAt: 'desc' }
        });
    }
    async getRecentPermissionChanges() {
        return await database_1.prisma.userLevelHistory.findMany({
            take: 10,
            orderBy: { createdAt: 'desc' },
            include: {
                user: {
                    select: { username: true }
                }
            }
        });
    }
    async logSecurityAction(adminId, action, details) {
        await database_1.prisma.adminOperationLog.create({
            data: {
                adminId,
                operationType: action,
                targetType: 'security',
                operationDetails: details
            }
        });
    }
}
exports.SecurityService = SecurityService;
//# sourceMappingURL=security.service.js.map