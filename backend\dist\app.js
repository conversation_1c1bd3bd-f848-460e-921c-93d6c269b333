"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.app = void 0;
exports.startServer = startServer;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const env_1 = require("./config/env");
const database_1 = require("./config/database");
const redis_1 = require("./config/redis");
const socket_1 = require("./config/socket");
const log_service_1 = require("./services/log.service");
const system_monitor_service_1 = require("./services/system-monitor.service");
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const upload_routes_1 = __importDefault(require("./routes/upload.routes"));
const admin_routes_1 = __importDefault(require("./routes/admin.routes"));
const user_routes_1 = __importDefault(require("./routes/user.routes"));
const levelApplication_routes_1 = __importDefault(require("./routes/levelApplication.routes"));
const invitation_code_routes_1 = __importDefault(require("./routes/invitation-code.routes"));
const test_routes_1 = __importDefault(require("./routes/test.routes"));
BigInt.prototype.toJSON = function () {
    return Number(this);
};
const app = (0, express_1.default)();
exports.app = app;
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: false,
}));
app.use((0, cors_1.default)({
    origin: env_1.config.frontend.url,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
if (env_1.config.security.enableRateLimiting) {
    const limiter = (0, express_rate_limit_1.default)({
        windowMs: env_1.config.security.rateLimitWindow,
        max: env_1.config.security.rateLimitMax,
        message: {
            error: {
                code: 'RATE_LIMIT_EXCEEDED',
                message: '请求过于频繁，请稍后再试',
                timestamp: new Date().toISOString(),
            }
        },
        standardHeaders: true,
        legacyHeaders: false,
    });
    app.use('/api/', limiter);
}
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: env_1.config.env,
        version: process.env.npm_package_version || '1.0.0',
    });
});
app.get('/api', (req, res) => {
    res.json({
        message: 'LoftChat 智能图片上传管理分发系统 API',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: {
            auth: '/api/auth',
            upload: '/api/upload',
            images: '/api/images',
            admin: '/api/admin',
        }
    });
});
app.use('/api/auth', auth_routes_1.default);
app.use('/api/upload', upload_routes_1.default);
app.use('/api/admin', admin_routes_1.default);
app.use('/api/admin/invitation-codes', invitation_code_routes_1.default);
app.use('/api/user', user_routes_1.default);
app.use('/api/level-application', levelApplication_routes_1.default);
app.use('/api/test', test_routes_1.default);
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            code: 'NOT_FOUND',
            message: '请求的资源不存在',
            path: req.originalUrl,
            timestamp: new Date().toISOString(),
        }
    });
});
app.use((error, req, res, next) => {
    console.error('❌ 服务器错误:', error);
    const isDevelopment = env_1.config.env === 'development';
    res.status(error.status || 500).json({
        success: false,
        error: {
            code: error.code || 'INTERNAL_ERROR',
            message: error.message || '服务器内部错误',
            timestamp: new Date().toISOString(),
            ...(isDevelopment && { stack: error.stack }),
        }
    });
});
async function startServer() {
    try {
        await (0, database_1.connectDatabase)();
        await (0, redis_1.connectRedis)();
        const server = (0, http_1.createServer)(app);
        const socketService = socket_1.SocketService.getInstance(server);
        log_service_1.LogService.initialize(socketService);
        const systemMonitor = new system_monitor_service_1.SystemMonitorService(socketService);
        systemMonitor.startMonitoring(30000);
        server.listen(env_1.config.port, () => {
            console.log(`🚀 服务器启动成功！`);
            console.log(`📍 地址: http://localhost:${env_1.config.port}`);
            console.log(`🌍 环境: ${env_1.config.env}`);
            console.log(`📊 健康检查: http://localhost:${env_1.config.port}/health`);
            console.log(`🔗 API文档: http://localhost:${env_1.config.port}/api`);
            console.log(`🔌 Socket.IO 实时日志服务已启动`);
            console.log(`📈 系统监控服务已启动`);
        });
        const gracefulShutdown = async (signal) => {
            console.log(`\n📡 收到 ${signal} 信号，开始优雅关闭...`);
            server.close(async () => {
                console.log('🔌 HTTP服务器已关闭');
                try {
                    systemMonitor.stopMonitoring();
                    console.log('📈 系统监控服务已停止');
                    socketService.close();
                    console.log('🔌 Socket.IO服务已关闭');
                    await (0, database_1.disconnectDatabase)();
                    await (0, redis_1.disconnectRedis)();
                    console.log('✅ 所有连接已关闭');
                    process.exit(0);
                }
                catch (error) {
                    console.error('❌ 关闭连接时出错:', error);
                    process.exit(1);
                }
            });
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    }
    catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}
process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    console.error('Promise:', promise);
    process.exit(1);
});
if (require.main === module) {
    startServer();
}
//# sourceMappingURL=app.js.map