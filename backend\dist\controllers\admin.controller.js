"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
const database_1 = require("../config/database");
const types_1 = require("../types");
const log_service_1 = require("../services/log.service");
const system_monitor_service_1 = require("../services/system-monitor.service");
const analytics_service_1 = require("../services/analytics.service");
const security_service_1 = require("../services/security.service");
class AdminController {
    static async getSystemStats(req, res) {
        try {
            const totalUsers = await database_1.prisma.user.count();
            const activeUsers = await database_1.prisma.user.count({
                where: {
                    status: 'active'
                }
            });
            const totalUploads = await database_1.prisma.userImage.count();
            const todayUploads = await database_1.prisma.userImage.count({
                where: {
                    createdAt: {
                        gte: new Date(new Date().setHours(0, 0, 0, 0))
                    }
                }
            });
            const storageStats = await database_1.prisma.image.aggregate({
                _sum: {
                    fileSize: true
                },
                where: {
                    isDeleted: false
                }
            });
            const systemLoad = {
                cpu: Math.floor(Math.random() * 100),
                memory: Math.floor(Math.random() * 100),
                disk: Math.floor(Math.random() * 100)
            };
            const onlineUsers = Math.floor(Math.random() * 50);
            const stats = {
                totalUsers,
                activeUsers,
                totalUploads,
                todayUploads,
                totalStorage: storageStats._sum.fileSize || 0,
                systemLoad,
                onlineUsers
            };
            res.json({
                success: true,
                data: stats,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取系统统计失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取系统统计失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateUserLevel(req, res) {
        try {
            const { id } = req.params;
            const { level, expiresAt } = req.body;
            const validLevels = ['free', 'vip1', 'vip2', 'vip3'];
            if (!validLevels.includes(level)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '无效的用户等级',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: Number(id) }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (user.role === 'admin') {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.FORBIDDEN,
                        message: '不能修改管理员的等级',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const updatedUser = await database_1.prisma.user.update({
                where: { id: Number(id) },
                data: {
                    userLevel: level,
                    levelExpiresAt: expiresAt ? new Date(expiresAt) : null
                },
                select: {
                    id: true,
                    username: true,
                    email: true,
                    userLevel: true,
                    levelExpiresAt: true
                }
            });
            res.json({
                success: true,
                data: updatedUser,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新用户等级失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新用户等级失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserDetails(req, res) {
        try {
            const { id } = req.params;
            const user = await database_1.prisma.user.findUnique({
                where: { id: Number(id) },
                include: {
                    userImages: {
                        include: {
                            image: true
                        },
                        orderBy: {
                            createdAt: 'desc'
                        },
                        take: 10
                    }
                }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            res.json({
                success: true,
                data: user,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户详情失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户详情失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getRealtimeLogs(req, res) {
        try {
            const { type = 'all', limit = 100 } = req.query;
            let logs = [];
            if (type === 'all' || type === 'system') {
                const systemLogs = await log_service_1.LogService.getSystemLogs({
                    page: 1,
                    limit: parseInt(limit) || 100,
                });
                logs = [...logs, ...systemLogs.logs.map(log => ({ ...log, type: 'system' }))];
            }
            if (type === 'all' || type === 'upload') {
                const uploadLogs = await log_service_1.LogService.getUploadLogs({
                    page: 1,
                    limit: parseInt(limit) || 100,
                });
                logs = [...logs, ...uploadLogs.logs.map(log => ({ ...log, type: 'upload' }))];
            }
            logs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
            res.json({
                success: true,
                data: logs.slice(0, parseInt(limit) || 100),
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取实时日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取实时日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getSystemLogs(req, res) {
        try {
            const { page = 1, limit = 50, levels, types, startDate, endDate } = req.query;
            const filters = {};
            if (levels) {
                filters.levels = levels.split(',');
            }
            if (types) {
                filters.types = types.split(',');
            }
            if (startDate) {
                filters.startDate = startDate;
            }
            if (endDate) {
                filters.endDate = endDate;
            }
            const result = await log_service_1.LogService.getSystemLogs({
                page: parseInt(page),
                limit: parseInt(limit),
                filters,
            });
            res.json({
                success: true,
                data: result,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取系统日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取系统日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUsers(req, res) {
        try {
            const { page = 1, limit = 20, search, status, role, userLevel, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const where = {};
            if (search) {
                where.OR = [
                    { username: { contains: search, mode: 'insensitive' } },
                    { email: { contains: search, mode: 'insensitive' } },
                    { displayName: { contains: search, mode: 'insensitive' } }
                ];
            }
            if (status) {
                where.status = status;
            }
            if (role) {
                where.role = role;
            }
            if (userLevel) {
                where.userLevel = userLevel;
            }
            const orderBy = {};
            orderBy[sortBy] = sortOrder;
            const [users, total] = await Promise.all([
                database_1.prisma.user.findMany({
                    where,
                    orderBy,
                    skip,
                    take,
                    select: {
                        id: true,
                        username: true,
                        email: true,
                        displayName: true,
                        role: true,
                        userLevel: true,
                        status: true,
                        avatarUrl: true,
                        createdAt: true,
                        updatedAt: true,
                        lastLoginAt: true,
                        lastActiveAt: true,
                        levelExpiresAt: true,
                        _count: {
                            select: {
                                userImages: true,
                                uploadLogs: true
                            }
                        }
                    }
                }),
                database_1.prisma.user.count({ where })
            ]);
            res.json({
                success: true,
                data: {
                    users,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户列表失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户列表失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserDetail(req, res) {
        try {
            const { userId } = req.params;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                include: {
                    userSettings: true,
                    _count: {
                        select: {
                            userImages: true,
                            uploadLogs: true,
                            activityLogs: true,
                            loginHistory: true
                        }
                    }
                }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.USER_NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            await log_service_1.LogService.logAdminOperation(adminId, 'view_user_detail', 'user', user.id, { viewedUser: user.username }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: user,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户详情失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户详情失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateUserStatus(req, res) {
        try {
            const { userId } = req.params;
            const { status, reason } = req.body;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const validStatuses = ['active', 'suspended', 'banned'];
            if (!validStatuses.includes(status)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '无效的用户状态',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                select: { id: true, username: true, status: true, role: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.USER_NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (user.role === 'admin') {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.PERMISSION_DENIED,
                        message: '不能修改管理员的状态',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const updatedUser = await database_1.prisma.user.update({
                where: { id: parseInt(userId) },
                data: {
                    status,
                    updatedAt: new Date()
                },
                select: {
                    id: true,
                    username: true,
                    status: true,
                    updatedAt: true
                }
            });
            await log_service_1.LogService.logAdminOperation(adminId, 'update_user_status', 'user', user.id, {
                oldStatus: user.status,
                newStatus: status,
                reason,
                targetUser: user.username
            }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: updatedUser,
                message: '用户状态更新成功',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新用户状态失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新用户状态失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateUserProfile(req, res) {
        try {
            const { userId } = req.params;
            const { displayName, bio, location, website, profileVisibility, allowDirectMessages, showOnlineStatus } = req.body;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                select: { id: true, username: true, role: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.USER_NOT_FOUND,
                        message: '用户不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (user.role === 'admin') {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.PERMISSION_DENIED,
                        message: '不能修改管理员的信息',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const updatedUser = await database_1.prisma.user.update({
                where: { id: parseInt(userId) },
                data: {
                    displayName: displayName || null,
                    bio: bio || null,
                    location: location || null,
                    website: website || null,
                    profileVisibility: profileVisibility || undefined,
                    allowDirectMessages: allowDirectMessages !== undefined ? allowDirectMessages : undefined,
                    showOnlineStatus: showOnlineStatus !== undefined ? showOnlineStatus : undefined,
                    updatedAt: new Date()
                },
                select: {
                    id: true,
                    username: true,
                    displayName: true,
                    bio: true,
                    location: true,
                    website: true,
                    profileVisibility: true,
                    allowDirectMessages: true,
                    showOnlineStatus: true,
                    updatedAt: true
                }
            });
            await log_service_1.LogService.logAdminOperation(adminId, 'update_user_profile', 'user', user.id, {
                changes: req.body,
                targetUser: user.username
            }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: updatedUser,
                message: '用户信息更新成功',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新用户信息失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新用户信息失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserActivityLogs(req, res) {
        try {
            const { userId } = req.params;
            const { page = 1, limit = 50 } = req.query;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const [logs, total] = await Promise.all([
                database_1.prisma.userActivityLog.findMany({
                    where: { userId: parseInt(userId) },
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take,
                    select: {
                        id: true,
                        activityType: true,
                        activityData: true,
                        ipAddress: true,
                        location: true,
                        deviceInfo: true,
                        createdAt: true
                    }
                }),
                database_1.prisma.userActivityLog.count({ where: { userId: parseInt(userId) } })
            ]);
            await log_service_1.LogService.logAdminOperation(adminId, 'view_user_activity_logs', 'user', parseInt(userId), { viewedLogsCount: logs.length }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: {
                    logs,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户活动日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户活动日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserLoginHistory(req, res) {
        try {
            const { userId } = req.params;
            const { page = 1, limit = 50 } = req.query;
            const adminId = parseInt(req.user.id);
            if (!userId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '用户ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const [history, total] = await Promise.all([
                database_1.prisma.userLoginHistory.findMany({
                    where: { userId: parseInt(userId) },
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take,
                    select: {
                        id: true,
                        loginType: true,
                        isSuccess: true,
                        failureReason: true,
                        ipAddress: true,
                        location: true,
                        deviceInfo: true,
                        createdAt: true
                    }
                }),
                database_1.prisma.userLoginHistory.count({ where: { userId: parseInt(userId) } })
            ]);
            await log_service_1.LogService.logAdminOperation(adminId, 'view_user_login_history', 'user', parseInt(userId), { viewedHistoryCount: history.length }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: {
                    history,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取用户登录历史失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户登录历史失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getLevelApplications(req, res) {
        try {
            const { page = 1, limit = 20, status = 'all', requestedLevel, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
            const skip = (parseInt(page) - 1) * parseInt(limit);
            const take = parseInt(limit);
            const where = {};
            if (status !== 'all') {
                where.status = status;
            }
            if (requestedLevel && requestedLevel !== 'all') {
                where.requestedLevel = requestedLevel;
            }
            const orderBy = {};
            orderBy[sortBy] = sortOrder;
            const [applications, total] = await Promise.all([
                database_1.prisma.userLevelApplication.findMany({
                    where,
                    orderBy,
                    skip,
                    take,
                    include: {
                        user: {
                            select: {
                                id: true,
                                username: true,
                                email: true,
                                displayName: true,
                                userLevel: true,
                                createdAt: true
                            }
                        },
                        admin: {
                            select: {
                                username: true,
                                displayName: true
                            }
                        }
                    }
                }),
                database_1.prisma.userLevelApplication.count({ where })
            ]);
            res.json({
                success: true,
                data: {
                    applications,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        totalPages: Math.ceil(total / parseInt(limit))
                    }
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取等级申请列表失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取等级申请列表失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async processLevelApplication(req, res) {
        try {
            const { applicationId } = req.params;
            const { action, comment } = req.body;
            const adminId = parseInt(req.user.id);
            if (!applicationId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '申请ID是必需的',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (!['approve', 'reject'].includes(action)) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '无效的操作类型',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const application = await database_1.prisma.userLevelApplication.findUnique({
                where: { id: parseInt(applicationId) },
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            userLevel: true,
                            email: true
                        }
                    }
                }
            });
            if (!application) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '申请记录不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (application.status !== 'pending') {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '该申请已被处理',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const newStatus = action === 'approve' ? 'approved' : 'rejected';
            const processedAt = new Date();
            const result = await database_1.prisma.$transaction(async (tx) => {
                const updatedApplication = await tx.userLevelApplication.update({
                    where: { id: parseInt(applicationId) },
                    data: {
                        status: newStatus,
                        adminId,
                        adminComment: comment || null,
                        processedAt,
                        updatedAt: new Date()
                    }
                });
                if (action === 'approve') {
                    await tx.user.update({
                        where: { id: application.userId },
                        data: {
                            userLevel: application.requestedLevel,
                            levelExpiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
                            updatedAt: new Date()
                        }
                    });
                    await tx.userLevelHistory.create({
                        data: {
                            userId: application.userId,
                            oldLevel: application.currentLevel,
                            newLevel: application.requestedLevel,
                            changedBy: adminId,
                            reason: `等级申请批准: ${comment || '无备注'}`
                        }
                    });
                }
                return updatedApplication;
            });
            await log_service_1.LogService.logAdminOperation(adminId, `level_application_${action}`, 'level_application', parseInt(applicationId), {
                targetUser: application.user.username,
                currentLevel: application.currentLevel,
                requestedLevel: application.requestedLevel,
                comment: comment || null
            }, req.ip, req.get('User-Agent'));
            await log_service_1.LogService.logSystemEvent(`level_application_${action}`, 'info', `管理员${action === 'approve' ? '批准' : '拒绝'}了用户 ${application.user.username} 的等级申请`, {
                applicationId: parseInt(applicationId),
                userId: application.userId,
                adminId,
                currentLevel: application.currentLevel,
                requestedLevel: application.requestedLevel,
                comment: comment || null
            });
            res.json({
                success: true,
                data: result,
                message: `申请已${action === 'approve' ? '批准' : '拒绝'}`,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('处理等级申请失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '处理等级申请失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async deleteLevelApplication(req, res) {
        try {
            const { applicationId } = req.params;
            const adminId = parseInt(req.user.id);
            if (!applicationId || isNaN(parseInt(applicationId))) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '无效的申请ID',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const application = await database_1.prisma.userLevelApplication.findUnique({
                where: { id: parseInt(applicationId) },
                include: {
                    user: {
                        select: { username: true, email: true }
                    }
                }
            });
            if (!application) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.NOT_FOUND,
                        message: '申请记录不存在',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (application.status === 'pending') {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '不能删除待处理的申请，请先处理申请',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            await database_1.prisma.userLevelApplication.delete({
                where: { id: parseInt(applicationId) }
            });
            await log_service_1.LogService.logAdminOperation(adminId, 'delete_level_application', 'level_application', parseInt(applicationId), {
                applicationId: parseInt(applicationId),
                userId: application.userId,
                username: application.user.username,
                currentLevel: application.currentLevel,
                requestedLevel: application.requestedLevel,
                status: application.status,
                reason: application.reason.substring(0, 100)
            }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                message: '申请记录已删除',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('删除等级申请失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '删除等级申请失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getProviders(req, res) {
        res.json({ success: true, data: [], timestamp: new Date().toISOString() });
    }
    static async createProvider(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async updateProvider(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async deleteProvider(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async getSystemMonitoring(req, res) {
        try {
            const monitorService = new system_monitor_service_1.SystemMonitorService();
            const systemSummary = await monitorService.getSystemSummary();
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            const recentEvents = await monitorService.getSystemEvents(undefined, yesterday, new Date(), 20);
            const oneHourAgo = new Date();
            oneHourAgo.setHours(oneHourAgo.getHours() - 1);
            const cpuHistory = await monitorService.getHistoricalMetrics('cpu_usage', oneHourAgo, new Date(), 60);
            const memoryHistory = await monitorService.getHistoricalMetrics('memory_usage', oneHourAgo, new Date(), 60);
            const response = {
                success: true,
                data: {
                    summary: systemSummary,
                    events: recentEvents,
                    charts: {
                        cpu: cpuHistory.map(item => ({
                            timestamp: item.createdAt,
                            value: Number(item.metricValue),
                            unit: item.unit
                        })),
                        memory: memoryHistory.map(item => ({
                            timestamp: item.createdAt,
                            value: Number(item.metricValue),
                            unit: item.unit
                        }))
                    },
                    thresholds: monitorService.getAlertThresholds()
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取系统监控数据失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取系统监控数据失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
    static async getMetricsHistory(req, res) {
        try {
            const { type } = req.params;
            const { startTime, endTime, limit = '100' } = req.query;
            if (!type) {
                const errorResponse = {
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '缺少指标类型参数',
                        timestamp: new Date().toISOString()
                    }
                };
                res.status(400).json(errorResponse);
                return;
            }
            const monitorService = new system_monitor_service_1.SystemMonitorService();
            const defaultEndTime = new Date();
            const defaultStartTime = new Date();
            defaultStartTime.setDate(defaultStartTime.getDate() - 1);
            const start = startTime ? new Date(startTime) : defaultStartTime;
            const end = endTime ? new Date(endTime) : defaultEndTime;
            const limitNum = parseInt(limit, 10);
            const metrics = await monitorService.getHistoricalMetrics(type, start, end, limitNum);
            const response = {
                success: true,
                data: {
                    metricType: type,
                    timeRange: { start, end },
                    data: metrics.map(item => ({
                        timestamp: item.createdAt,
                        value: Number(item.metricValue),
                        unit: item.unit,
                        serverInstance: item.serverInstance
                    }))
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取指标历史数据失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取指标历史数据失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
    static async getSystemEvents(req, res) {
        try {
            const { level, startTime, endTime, limit = '50' } = req.query;
            const monitorService = new system_monitor_service_1.SystemMonitorService();
            const defaultEndTime = new Date();
            const defaultStartTime = new Date();
            defaultStartTime.setDate(defaultStartTime.getDate() - 1);
            const start = startTime ? new Date(startTime) : defaultStartTime;
            const end = endTime ? new Date(endTime) : defaultEndTime;
            const limitNum = parseInt(limit, 10);
            const events = await monitorService.getSystemEvents(level, start, end, limitNum);
            const response = {
                success: true,
                data: {
                    events,
                    filters: {
                        level: level || 'all',
                        timeRange: { start, end }
                    }
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取系统事件日志失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取系统事件日志失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
    static async updateAlertThresholds(req, res) {
        try {
            const { cpu, memory, disk, errorRate } = req.body;
            const adminUser = req.user;
            const thresholds = {};
            if (cpu !== undefined)
                thresholds.cpu = Math.max(0, Math.min(100, cpu));
            if (memory !== undefined)
                thresholds.memory = Math.max(0, Math.min(100, memory));
            if (disk !== undefined)
                thresholds.disk = Math.max(0, Math.min(100, disk));
            if (errorRate !== undefined)
                thresholds.errorRate = Math.max(0, Math.min(100, errorRate));
            await log_service_1.LogService.logAdminOperation(adminUser.id, 'update_alert_thresholds', 'system', undefined, { oldThresholds: {}, newThresholds: thresholds }, req.ip, req.get('User-Agent'));
            const response = {
                success: true,
                data: {
                    message: '告警阈值已更新',
                    thresholds
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('更新告警阈值失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新告警阈值失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
    static async cleanupMonitoringData(req, res) {
        try {
            const { daysToKeep = 30 } = req.body;
            const adminUser = req.user;
            const monitorService = new system_monitor_service_1.SystemMonitorService();
            await monitorService.cleanupOldData(daysToKeep);
            await log_service_1.LogService.logAdminOperation(adminUser.id, 'cleanup_monitoring_data', 'system', undefined, { daysToKeep }, req.ip, req.get('User-Agent'));
            const response = {
                success: true,
                data: {
                    message: `已清理 ${daysToKeep} 天前的监控数据`
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('清理监控数据失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '清理监控数据失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
    static async getDataRetentionConfig(req, res) {
        try {
            const monitorService = new system_monitor_service_1.SystemMonitorService();
            const config = monitorService.getDataRetentionConfig();
            const response = {
                success: true,
                data: config
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取数据保留策略失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取数据保留策略失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
    static async updateDataRetentionConfig(req, res) {
        try {
            const { metricsRetentionHours, eventsRetentionDays, cleanupIntervalMinutes } = req.body;
            const adminUser = req.user;
            const config = {};
            if (metricsRetentionHours !== undefined)
                config.metricsRetentionHours = Math.max(1, metricsRetentionHours);
            if (eventsRetentionDays !== undefined)
                config.eventsRetentionDays = Math.max(1, eventsRetentionDays);
            if (cleanupIntervalMinutes !== undefined)
                config.cleanupIntervalMinutes = Math.max(5, cleanupIntervalMinutes);
            const monitorService = new system_monitor_service_1.SystemMonitorService();
            monitorService.setDataRetentionConfig(config);
            await log_service_1.LogService.logAdminOperation(adminUser.id, 'update_data_retention_config', 'system', undefined, { newConfig: config }, req.ip, req.get('User-Agent'));
            const response = {
                success: true,
                data: {
                    message: '数据保留策略已更新',
                    config: monitorService.getDataRetentionConfig()
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('更新数据保留策略失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新数据保留策略失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
    static async triggerDataCleanup(req, res) {
        try {
            const adminUser = req.user;
            const monitorService = new system_monitor_service_1.SystemMonitorService();
            await monitorService.performDataCleanup();
            await log_service_1.LogService.logAdminOperation(adminUser.id, 'trigger_data_cleanup', 'system', undefined, { triggeredAt: new Date().toISOString() }, req.ip, req.get('User-Agent'));
            const response = {
                success: true,
                data: {
                    message: '数据清理任务已执行'
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('手动数据清理失败:', error);
            const errorResponse = {
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '手动数据清理失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                }
            };
            res.status(500).json(errorResponse);
        }
    }
    static async getIPSecurity(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async addIPToBlacklist(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async removeIPFromBlacklist(req, res) {
        res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
    }
    static async getAnalyticsOverview(req, res) {
        try {
            const { timeRange = 'month' } = req.query;
            const analyticsService = new analytics_service_1.AnalyticsService();
            const overview = await analyticsService.getAnalyticsOverview(timeRange);
            const response = {
                success: true,
                data: overview
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取数据分析概览失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取数据分析概览失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserAnalytics(req, res) {
        try {
            const { timeRange = 'month' } = req.query;
            const analyticsService = new analytics_service_1.AnalyticsService();
            const userAnalytics = await analyticsService.getUserAnalytics(timeRange);
            const response = {
                success: true,
                data: userAnalytics
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取用户行为分析失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户行为分析失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUploadAnalytics(req, res) {
        try {
            const { timeRange = 'month' } = req.query;
            const analyticsService = new analytics_service_1.AnalyticsService();
            const uploadAnalytics = await analyticsService.getUploadAnalytics(timeRange);
            const response = {
                success: true,
                data: uploadAnalytics
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取上传数据分析失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取上传数据分析失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getRealtimeAnalytics(req, res) {
        try {
            const analyticsService = new analytics_service_1.AnalyticsService();
            const realtimeStats = await analyticsService.getRealtimeStats();
            const response = {
                success: true,
                data: realtimeStats
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取实时统计数据失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取实时统计数据失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async clearRealtimeLogs(req, res) {
        try {
            const { logType } = req.body;
            let deleteCondition = {};
            if (logType && logType !== 'all') {
                deleteCondition.eventType = logType;
            }
            const deletedEventLogs = await database_1.prisma.systemEventLog.deleteMany({
                where: deleteCondition
            });
            await log_service_1.LogService.logAdminOperation(Number(req.user?.id) || 0, 'clear_realtime_logs', 'system_logs', undefined, {
                logType: logType || 'all',
                deletedCount: deletedEventLogs.count,
                timestamp: new Date().toISOString()
            }, req.ip || 'unknown');
            const response = {
                success: true,
                data: {
                    deletedCount: deletedEventLogs.count,
                    logType: logType || 'all',
                    message: `成功清除 ${deletedEventLogs.count} 条日志记录`
                }
            };
            res.json(response);
        }
        catch (error) {
            console.error('清除实时日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '清除实时日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async clearSystemLogs(req, res) {
        try {
            const adminId = parseInt(req.user.id);
            const { logTypes, olderThan } = req.body;
            const deleteConditions = [];
            if (logTypes && Array.isArray(logTypes) && logTypes.length > 0) {
                if (logTypes.includes('system')) {
                    const systemDeleteCondition = {};
                    if (olderThan) {
                        systemDeleteCondition.createdAt = { lt: new Date(olderThan) };
                    }
                    deleteConditions.push(database_1.prisma.systemEventLog.deleteMany({ where: systemDeleteCondition }));
                }
                if (logTypes.includes('upload')) {
                    const uploadDeleteCondition = {};
                    if (olderThan) {
                        uploadDeleteCondition.createdAt = { lt: new Date(olderThan) };
                    }
                    deleteConditions.push(database_1.prisma.uploadLog.deleteMany({ where: uploadDeleteCondition }));
                }
                if (logTypes.includes('access')) {
                    const accessDeleteCondition = {};
                    if (olderThan) {
                        accessDeleteCondition.createdAt = { lt: new Date(olderThan) };
                    }
                    deleteConditions.push(database_1.prisma.accessLog.deleteMany({ where: accessDeleteCondition }));
                }
                if (logTypes.includes('admin')) {
                    const adminDeleteCondition = {};
                    if (olderThan) {
                        adminDeleteCondition.createdAt = { lt: new Date(olderThan) };
                    }
                    deleteConditions.push(database_1.prisma.adminOperationLog.deleteMany({ where: adminDeleteCondition }));
                }
            }
            else {
                const deleteCondition = {};
                if (olderThan) {
                    deleteCondition.createdAt = { lt: new Date(olderThan) };
                }
                deleteConditions.push(database_1.prisma.systemEventLog.deleteMany({ where: deleteCondition }), database_1.prisma.uploadLog.deleteMany({ where: deleteCondition }), database_1.prisma.accessLog.deleteMany({ where: deleteCondition }), database_1.prisma.adminOperationLog.deleteMany({ where: deleteCondition }));
            }
            const results = await Promise.all(deleteConditions);
            const totalDeleted = results.reduce((sum, result) => sum + result.count, 0);
            await log_service_1.LogService.logAdminOperation(adminId, 'clear_system_logs', 'system', undefined, {
                logTypes: logTypes || ['all'],
                olderThan,
                deletedCount: totalDeleted
            }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                data: {
                    deletedCount: totalDeleted,
                    logTypes: logTypes || ['all'],
                    olderThan
                },
                message: `成功清理了 ${totalDeleted} 条日志记录`,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('清理系统日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '清理系统日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getIPSecurityOverview(req, res) {
        try {
            const securityService = new security_service_1.SecurityService();
            const overview = await securityService.getIPSecurityOverview();
            const response = {
                success: true,
                data: overview
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取IP风控概览失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取IP风控概览失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getIPRiskLogs(req, res) {
        try {
            const { page = '1', limit = '20', ipAddress, actionType, isBlocked, startDate, endDate } = req.query;
            const securityService = new security_service_1.SecurityService();
            const options = {
                page: parseInt(page),
                limit: parseInt(limit)
            };
            if (ipAddress)
                options.ipAddress = ipAddress;
            if (actionType)
                options.actionType = actionType;
            if (isBlocked === 'true')
                options.isBlocked = true;
            if (isBlocked === 'false')
                options.isBlocked = false;
            if (startDate)
                options.startDate = new Date(startDate);
            if (endDate)
                options.endDate = new Date(endDate);
            const result = await securityService.getIPRiskLogs(options);
            const response = {
                success: true,
                data: result
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取IP风控日志失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取IP风控日志失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getIPRiskRules(req, res) {
        try {
            const securityService = new security_service_1.SecurityService();
            const rules = await securityService.getIPRiskRules();
            const response = {
                success: true,
                data: rules
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取IP风控规则失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取IP风控规则失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async createIPRiskRule(req, res) {
        try {
            const { ruleName, ruleType, timeWindow, maxAttempts, blockDuration, isActive } = req.body;
            if (!ruleName || !ruleType || !timeWindow || !maxAttempts || !blockDuration) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '缺少必填字段',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const securityService = new security_service_1.SecurityService();
            const rule = await securityService.createIPRiskRule({
                ruleName,
                ruleType,
                timeWindow: parseInt(timeWindow),
                maxAttempts: parseInt(maxAttempts),
                blockDuration: parseInt(blockDuration),
                isActive
            });
            await log_service_1.LogService.logAdminOperation(Number(req.user?.id) || 0, 'create_ip_risk_rule', 'security', rule.id, { ruleName, ruleType }, req.ip || 'unknown');
            const response = {
                success: true,
                data: rule
            };
            res.json(response);
        }
        catch (error) {
            console.error('创建IP风控规则失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '创建IP风控规则失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateIPRiskRule(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            if (!id) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '缺少规则ID',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const securityService = new security_service_1.SecurityService();
            const rule = await securityService.updateIPRiskRule(parseInt(id), updateData);
            await log_service_1.LogService.logAdminOperation(Number(req.user?.id) || 0, 'update_ip_risk_rule', 'security', rule.id, updateData, req.ip || 'unknown');
            const response = {
                success: true,
                data: rule
            };
            res.json(response);
        }
        catch (error) {
            console.error('更新IP风控规则失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新IP风控规则失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async deleteIPRiskRule(req, res) {
        try {
            const { id } = req.params;
            if (!id) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '缺少规则ID',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const securityService = new security_service_1.SecurityService();
            await securityService.deleteIPRiskRule(parseInt(id));
            await log_service_1.LogService.logAdminOperation(Number(req.user?.id) || 0, 'delete_ip_risk_rule', 'security', parseInt(id), { ruleId: id }, req.ip || 'unknown');
            const response = {
                success: true,
                data: { message: '风控规则删除成功' }
            };
            res.json(response);
        }
        catch (error) {
            console.error('删除IP风控规则失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '删除IP风控规则失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async blockIP(req, res) {
        try {
            const { ipAddress, blockDuration, reason } = req.body;
            if (!ipAddress || !blockDuration || !reason) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '缺少必填字段',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const securityService = new security_service_1.SecurityService();
            const blockRecord = await securityService.blockIP({
                ipAddress,
                blockDuration: parseInt(blockDuration),
                reason,
                adminId: Number(req.user?.id) || 0
            });
            await log_service_1.LogService.logAdminOperation(Number(req.user?.id) || 0, 'manual_block_ip', 'security', blockRecord.id, { ipAddress, blockDuration, reason }, req.ip || 'unknown');
            const response = {
                success: true,
                data: blockRecord
            };
            res.json(response);
        }
        catch (error) {
            console.error('封禁IP失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '封禁IP失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async unblockIP(req, res) {
        try {
            const { ipAddress } = req.body;
            if (!ipAddress) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '缺少IP地址',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const securityService = new security_service_1.SecurityService();
            const unblockRecord = await securityService.unblockIP(ipAddress, Number(req.user?.id) || 0);
            await log_service_1.LogService.logAdminOperation(Number(req.user?.id) || 0, 'manual_unblock_ip', 'security', unblockRecord.id, { ipAddress }, req.ip || 'unknown');
            const response = {
                success: true,
                data: unblockRecord
            };
            res.json(response);
        }
        catch (error) {
            console.error('解封IP失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '解封IP失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserPermissionsOverview(req, res) {
        try {
            const securityService = new security_service_1.SecurityService();
            const overview = await securityService.getUserPermissionsOverview();
            const response = {
                success: true,
                data: overview
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取用户权限概览失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户权限概览失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getUserSessions(req, res) {
        try {
            const { page = '1', limit = '20', userId, ipAddress, isActive } = req.query;
            const securityService = new security_service_1.SecurityService();
            const options = {
                page: parseInt(page),
                limit: parseInt(limit)
            };
            if (userId)
                options.userId = parseInt(userId);
            if (ipAddress)
                options.ipAddress = ipAddress;
            if (isActive === 'true')
                options.isActive = true;
            if (isActive === 'false')
                options.isActive = false;
            const result = await securityService.getUserSessions(options);
            const response = {
                success: true,
                data: result
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取用户会话列表失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取用户会话列表失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async revokeUserSession(req, res) {
        try {
            const { sessionId } = req.params;
            if (!sessionId) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '缺少会话ID',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const securityService = new security_service_1.SecurityService();
            const session = await securityService.revokeUserSession(parseInt(sessionId), Number(req.user?.id) || 0);
            const response = {
                success: true,
                data: session
            };
            res.json(response);
        }
        catch (error) {
            console.error('注销用户会话失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '注销用户会话失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async getSecurityConfigs(req, res) {
        try {
            const securityService = new security_service_1.SecurityService();
            const configs = await securityService.getSecurityConfigs();
            const response = {
                success: true,
                data: configs
            };
            res.json(response);
        }
        catch (error) {
            console.error('获取安全配置失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '获取安全配置失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
    static async updateSecurityConfig(req, res) {
        try {
            const { configKey, configValue } = req.body;
            if (!configKey || configValue === undefined) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.VALIDATION_ERROR,
                        message: '缺少配置键或配置值',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const securityService = new security_service_1.SecurityService();
            const config = await securityService.updateSecurityConfig(configKey, configValue, Number(req.user?.id) || 0);
            const response = {
                success: true,
                data: config
            };
            res.json(response);
        }
        catch (error) {
            console.error('更新安全配置失败:', error);
            res.status(500).json({
                success: false,
                error: {
                    code: types_1.ErrorCodes.INTERNAL_ERROR,
                    message: '更新安全配置失败',
                    timestamp: new Date().toISOString()
                }
            });
        }
    }
}
exports.AdminController = AdminController;
//# sourceMappingURL=admin.controller.js.map