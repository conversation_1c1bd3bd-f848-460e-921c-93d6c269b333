-- 启用邀请码系统的SQL脚本

-- 删除现有的邀请码系统配置（如果存在）
DELETE FROM system_configs WHERE key = 'invitation_system';

-- 插入新的邀请码系统配置
INSERT INTO system_configs (key, value, description, updated_at) 
VALUES (
  'invitation_system',
  '{"enabled": true, "requireInvitationCode": false, "allowBatchGeneration": true, "maxBatchSize": 100, "defaultExpirationDays": null, "autoCleanupExpired": true}',
  '邀请码系统配置',
  NOW()
);

-- 查询确认配置已插入
SELECT * FROM system_configs WHERE key = 'invitation_system';
