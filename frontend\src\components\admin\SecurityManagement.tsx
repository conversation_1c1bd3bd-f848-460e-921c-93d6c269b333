import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { adminService } from '../../services/admin.service';
import type {
  IPSecurityOverview,
  IPRiskLog,
  IPRiskRule,
  UserPermissionsOverview,
  UserSession,
  SecurityConfig
} from '../../services/admin.service';

type SecurityTab = 'ip-security' | 'permissions' | 'sessions' | 'configs';

export function SecurityManagement() {
  const [activeTab, setActiveTab] = useState<SecurityTab>('ip-security');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 数据状态
  const [ipOverview, setIpOverview] = useState<IPSecurityOverview | null>(null);
  const [permissionsOverview, setPermissionsOverview] = useState<UserPermissionsOverview | null>(null);

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [ipData, permissionsData] = await Promise.all([
        adminService.getIPSecurityOverview(),
        adminService.getUserPermissionsOverview()
      ]);

      setIpOverview(ipData);
      setPermissionsOverview(permissionsData);
    } catch (error) {
      console.error('加载安全管理数据失败:', error);
      setError('加载安全管理数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { key: 'ip-security' as SecurityTab, label: 'IP风控管理', icon: '🛡️' },
    { key: 'permissions' as SecurityTab, label: '用户权限', icon: '👥' },
    { key: 'sessions' as SecurityTab, label: '会话管理', icon: '🔐' },
    { key: 'configs' as SecurityTab, label: '安全配置', icon: '⚙️' }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="loading-spinner w-8 h-8 mx-auto mb-4" />
            <p className="text-gray-600">加载安全管理数据中...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={loadSecurityData}>重新加载</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和控制栏 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-xl font-semibold text-gray-900">安全管理</h1>
          <p className="text-sm text-gray-600 mt-1">系统安全监控与管理中心</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={loadSecurityData}
            size="sm"
            variant="outline"
          >
            刷新数据
          </Button>
        </div>
      </div>

      {/* 安全概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {ipOverview && (
          <>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-gray-600">总IP数</p>
                    <p className="text-lg font-semibold text-gray-900">{ipOverview.summary.totalIPs}</p>
                  </div>
                  <div className="text-2xl">🌐</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-gray-600">封禁IP</p>
                    <p className="text-lg font-semibold text-red-600">{ipOverview.summary.blockedIPs}</p>
                  </div>
                  <div className="text-2xl">🚫</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-gray-600">风险IP</p>
                    <p className="text-lg font-semibold text-orange-600">{ipOverview.summary.riskIPs}</p>
                  </div>
                  <div className="text-2xl">⚠️</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-gray-600">活跃规则</p>
                    <p className="text-lg font-semibold text-blue-600">{ipOverview.summary.activeRules}</p>
                  </div>
                  <div className="text-2xl">📋</div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      {activeTab === 'ip-security' && ipOverview && (
        <IPSecurityTab ipOverview={ipOverview} onRefresh={loadSecurityData} />
      )}

      {activeTab === 'permissions' && permissionsOverview && (
        <PermissionsTab permissionsOverview={permissionsOverview} onRefresh={loadSecurityData} />
      )}

      {activeTab === 'sessions' && (
        <SessionsTab onRefresh={loadSecurityData} />
      )}

      {activeTab === 'configs' && (
        <ConfigsTab onRefresh={loadSecurityData} />
      )}
    </div>
  );
}

// IP安全管理标签页组件
function IPSecurityTab({ ipOverview, onRefresh }: {
  ipOverview: IPSecurityOverview;
  onRefresh: () => void;
}) {
  const [ipLogs, setIpLogs] = useState<IPRiskLog[]>([]);
  const [ipRules, setIpRules] = useState<IPRiskRule[]>([]);
  const [loading, setLoading] = useState(false);
  const [showBlockDialog, setShowBlockDialog] = useState(false);
  const [showRuleDialog, setShowRuleDialog] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const [message, setMessage] = useState('');

  // 封禁IP表单状态
  const [blockFormData, setBlockFormData] = useState({
    ipAddress: '',
    blockDuration: 60, // 默认1小时（分钟）
    reason: ''
  });
  const [blockFormErrors, setBlockFormErrors] = useState({
    ipAddress: '',
    reason: ''
  });

  useEffect(() => {
    loadIPData();
  }, []);

  const loadIPData = async () => {
    try {
      setLoading(true);
      const [logsData, rulesData] = await Promise.all([
        adminService.getIPRiskLogs({ limit: 10 }),
        adminService.getIPRiskRules()
      ]);
      setIpLogs(logsData.logs);
      setIpRules(rulesData);
    } catch (error) {
      console.error('加载IP数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const showSuccessMessage = (msg: string) => {
    setMessage(msg);
    setShowMessage(true);
    setTimeout(() => {
      setShowMessage(false);
      setMessage('');
    }, 3000);
  };

  // IP地址格式验证
  const validateIPAddress = (ip: string): boolean => {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  };

  // 验证封禁表单
  const validateBlockForm = (): boolean => {
    const errors = { ipAddress: '', reason: '' };
    let isValid = true;

    if (!blockFormData.ipAddress.trim()) {
      errors.ipAddress = 'IP地址不能为空';
      isValid = false;
    } else if (!validateIPAddress(blockFormData.ipAddress.trim())) {
      errors.ipAddress = '请输入有效的IPv4地址格式';
      isValid = false;
    }

    if (!blockFormData.reason.trim()) {
      errors.reason = '封禁原因不能为空';
      isValid = false;
    } else if (blockFormData.reason.trim().length < 5) {
      errors.reason = '封禁原因至少需要5个字符';
      isValid = false;
    }

    setBlockFormErrors(errors);
    return isValid;
  };

  // 重置封禁表单
  const resetBlockForm = () => {
    setBlockFormData({
      ipAddress: '',
      blockDuration: 60,
      reason: ''
    });
    setBlockFormErrors({
      ipAddress: '',
      reason: ''
    });
  };

  const handleBlockIP = async () => {
    if (!validateBlockForm()) {
      return;
    }

    try {
      await adminService.blockIP({
        ipAddress: blockFormData.ipAddress.trim(),
        blockDuration: blockFormData.blockDuration,
        reason: blockFormData.reason.trim()
      });
      await loadIPData();
      onRefresh();
      setShowBlockDialog(false);
      resetBlockForm();
      showSuccessMessage('IP封禁成功');
    } catch (error) {
      console.error('封禁IP失败:', error);
      showSuccessMessage('封禁IP失败，请稍后重试');
    }
  };

  const handleUnblockIP = async (ipAddress: string) => {
    try {
      await adminService.unblockIP(ipAddress);
      await loadIPData();
      onRefresh();
      showSuccessMessage('IP解封成功');
    } catch (error) {
      console.error('解封IP失败:', error);
      showSuccessMessage('解封IP失败，请稍后重试');
    }
  };

  return (
    <div className="space-y-6">
      {/* 最近IP活动 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>最近IP活动</CardTitle>
            <Button onClick={loadIPData} size="sm" variant="outline">
              刷新
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {ipOverview.recentActivity.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">📊</div>
              <p>暂无IP活动记录</p>
            </div>
          ) : (
            <div className="space-y-3">
              {ipOverview.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.isBlocked ? 'bg-red-500' :
                      activity.riskScore >= 50 ? 'bg-orange-500' : 'bg-green-500'
                    }`} />
                    <div>
                      <p className="font-medium text-sm">{activity.ipAddress}</p>
                      <p className="text-xs text-gray-600">
                        {activity.actionType} • 风险评分: {activity.riskScore}
                        {activity.user && ` • 用户: ${activity.user.username}`}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {activity.isBlocked ? (
                      <Badge variant="destructive">已封禁</Badge>
                    ) : activity.riskScore >= 50 ? (
                      <Badge variant="warning">高风险</Badge>
                    ) : (
                      <Badge variant="success">正常</Badge>
                    )}
                    <span className="text-xs text-gray-500">
                      {new Date(activity.createdAt).toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 风控规则 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>风控规则</CardTitle>
            <Button onClick={() => setShowRuleDialog(true)} size="sm">
              添加规则
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {ipOverview.activeRules.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">📋</div>
              <p>暂无风控规则</p>
            </div>
          ) : (
            <div className="space-y-3">
              {ipOverview.activeRules.map((rule) => (
                <div key={rule.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{rule.ruleName}</p>
                    <p className="text-xs text-gray-600">类型: {rule.ruleType}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={rule.isActive ? 'success' : 'secondary'}>
                      {rule.isActive ? '启用' : '禁用'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* IP操作区域 */}
      <Card>
        <CardHeader>
          <CardTitle>IP操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={() => setShowBlockDialog(true)}
              variant="outline"
              className="text-red-600 hover:text-red-700"
            >
              🚫 手动封禁IP
            </Button>
            <Button
              onClick={loadIPData}
              variant="outline"
            >
              📊 查看详细日志
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 手动封禁IP对话框 */}
      {showBlockDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-lg">
            <h3 className="text-lg font-semibold mb-4 text-gray-900">手动封禁IP</h3>

            <div className="space-y-4">
              {/* IP地址输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  IP地址 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={blockFormData.ipAddress}
                  onChange={(e) => {
                    setBlockFormData(prev => ({ ...prev, ipAddress: e.target.value }));
                    if (blockFormErrors.ipAddress) {
                      setBlockFormErrors(prev => ({ ...prev, ipAddress: '' }));
                    }
                  }}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    blockFormErrors.ipAddress ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="请输入要封禁的IP地址，如：***********"
                />
                {blockFormErrors.ipAddress && (
                  <p className="text-red-500 text-xs mt-1">{blockFormErrors.ipAddress}</p>
                )}
              </div>

              {/* 封禁时长选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  封禁时长
                </label>
                <select
                  value={blockFormData.blockDuration}
                  onChange={(e) => setBlockFormData(prev => ({ ...prev, blockDuration: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={60}>1小时</option>
                  <option value={360}>6小时</option>
                  <option value={1440}>24小时</option>
                  <option value={10080}>7天</option>
                  <option value={43200}>30天</option>
                  <option value={525600}>永久（1年）</option>
                </select>
              </div>

              {/* 封禁原因输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  封禁原因 <span className="text-red-500">*</span>
                </label>
                <textarea
                  value={blockFormData.reason}
                  onChange={(e) => {
                    setBlockFormData(prev => ({ ...prev, reason: e.target.value }));
                    if (blockFormErrors.reason) {
                      setBlockFormErrors(prev => ({ ...prev, reason: '' }));
                    }
                  }}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none ${
                    blockFormErrors.reason ? 'border-red-500' : 'border-gray-300'
                  }`}
                  rows={3}
                  placeholder="请输入封禁原因，如：恶意攻击、频繁违规操作等"
                />
                {blockFormErrors.reason && (
                  <p className="text-red-500 text-xs mt-1">{blockFormErrors.reason}</p>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3 mt-6">
              <Button
                onClick={() => {
                  setShowBlockDialog(false);
                  resetBlockForm();
                }}
                variant="outline"
              >
                取消
              </Button>
              <Button
                onClick={handleBlockIP}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                确认封禁
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 消息提示弹窗 */}
      {showMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-sm mx-4 shadow-lg">
            <div className="text-center">
              <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4 ${
                message.includes('失败') ? 'bg-red-100' : 'bg-green-100'
              }`}>
                {message.includes('失败') ? (
                  <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {message.includes('失败') ? '操作失败' : '操作成功'}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {message}
              </p>
              <Button
                onClick={() => {
                  setShowMessage(false);
                  setMessage('');
                }}
                className="w-full"
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// 用户权限管理标签页组件
function PermissionsTab({ permissionsOverview, onRefresh }: {
  permissionsOverview: UserPermissionsOverview;
  onRefresh: () => void;
}) {
  return (
    <div className="space-y-6">
      {/* 权限统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">总用户数</p>
                <p className="text-lg font-semibold text-gray-900">{permissionsOverview.summary.totalUsers}</p>
              </div>
              <div className="text-2xl">👥</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">管理员</p>
                <p className="text-lg font-semibold text-blue-600">{permissionsOverview.summary.adminUsers}</p>
              </div>
              <div className="text-2xl">👑</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">VIP用户</p>
                <p className="text-lg font-semibold text-green-600">{permissionsOverview.summary.vipUsers}</p>
              </div>
              <div className="text-2xl">💎</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-600">封禁用户</p>
                <p className="text-lg font-semibold text-red-600">{permissionsOverview.summary.bannedUsers}</p>
              </div>
              <div className="text-2xl">🚫</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 最近权限变更 */}
      <Card>
        <CardHeader>
          <CardTitle>最近权限变更</CardTitle>
        </CardHeader>
        <CardContent>
          {permissionsOverview.recentChanges.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">📝</div>
              <p>暂无权限变更记录</p>
            </div>
          ) : (
            <div className="space-y-3">
              {permissionsOverview.recentChanges.map((change) => (
                <div key={change.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{change.user.username}</p>
                    <p className="text-xs text-gray-600">
                      {change.oldLevel} → {change.newLevel}
                    </p>
                  </div>
                  <span className="text-xs text-gray-500">
                    {new Date(change.createdAt).toLocaleString()}
                  </span>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// 会话管理标签页组件
function SessionsTab({ onRefresh }: { onRefresh: () => void }) {
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [showMessage, setShowMessage] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setLoading(true);
      const data = await adminService.getUserSessions({ limit: 20 });
      setSessions(data.sessions);
    } catch (error) {
      console.error('加载会话数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const showSuccessMessage = (msg: string) => {
    setMessage(msg);
    setShowMessage(true);
    setTimeout(() => {
      setShowMessage(false);
      setMessage('');
    }, 3000);
  };

  const handleRevokeSession = async (sessionId: number) => {
    if (!confirm('确定要强制注销此会话吗？')) return;

    try {
      await adminService.revokeUserSession(sessionId);
      await loadSessions();
      onRefresh();
      showSuccessMessage('会话注销成功');
    } catch (error) {
      console.error('注销会话失败:', error);
      showSuccessMessage('注销会话失败，请稍后重试');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>用户会话</CardTitle>
            <Button onClick={loadSessions} size="sm" variant="outline">
              刷新
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="loading-spinner w-6 h-6 mx-auto mb-2" />
              <p className="text-gray-600">加载中...</p>
            </div>
          ) : sessions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🔐</div>
              <p>暂无活跃会话</p>
            </div>
          ) : (
            <div className="space-y-3">
              {sessions.map((session) => (
                <div key={session.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <div className={`w-2 h-2 rounded-full ${session.isActive ? 'bg-green-500' : 'bg-gray-400'}`} />
                      <div>
                        <p className="font-medium text-sm">{session.user.username}</p>
                        <p className="text-xs text-gray-600">
                          {session.ipAddress} • {session.user.role} • {session.user.userLevel}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        创建: {new Date(session.createdAt).toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">
                        过期: {new Date(session.expiresAt).toLocaleString()}
                      </p>
                    </div>
                    {session.isActive && (
                      <Button
                        onClick={() => handleRevokeSession(session.id)}
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700"
                      >
                        注销
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 消息提示弹窗 */}
      {showMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-sm mx-4 shadow-lg">
            <div className="text-center">
              <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4 ${
                message.includes('失败') ? 'bg-red-100' : 'bg-green-100'
              }`}>
                {message.includes('失败') ? (
                  <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {message.includes('失败') ? '操作失败' : '操作成功'}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {message}
              </p>
              <Button
                onClick={() => {
                  setShowMessage(false);
                  setMessage('');
                }}
                className="w-full"
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// 安全配置标签页组件
function ConfigsTab({ onRefresh }: { onRefresh: () => void }) {
  const [configs, setConfigs] = useState<SecurityConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingConfig, setEditingConfig] = useState<SecurityConfig | null>(null);
  const [editValue, setEditValue] = useState<string>('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      setLoading(true);
      const data = await adminService.getSecurityConfigs();
      setConfigs(data);
    } catch (error) {
      console.error('加载安全配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateConfig = async (configKey: string, configValue: any) => {
    try {
      await adminService.updateSecurityConfig(configKey, configValue);
      await loadConfigs();
      onRefresh();
      setEditingConfig(null);
      setEditValue('');

      // 显示成功消息
      setSuccessMessage('配置更新成功');
      setShowSuccessMessage(true);

      // 3秒后自动隐藏
      setTimeout(() => {
        setShowSuccessMessage(false);
        setSuccessMessage('');
      }, 3000);
    } catch (error) {
      console.error('更新配置失败:', error);
      // 显示错误消息
      setSuccessMessage('更新配置失败，请稍后重试');
      setShowSuccessMessage(true);

      // 3秒后自动隐藏
      setTimeout(() => {
        setShowSuccessMessage(false);
        setSuccessMessage('');
      }, 3000);
    }
  };

  const getConfigDisplayValue = (value: any) => {
    if (typeof value === 'boolean') {
      return value ? '启用' : '禁用';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  const defaultConfigs = [
    {
      configKey: 'enable_ip_warning',
      configValue: true,
      description: '启用新IP登录警告'
    },
    {
      configKey: 'enable_rate_limiting',
      configValue: true,
      description: '启用API频率限制'
    },
    {
      configKey: 'max_login_attempts',
      configValue: 5,
      description: '最大登录尝试次数'
    },
    {
      configKey: 'session_timeout',
      configValue: 24,
      description: '会话超时时间（小时）'
    }
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>安全配置</CardTitle>
            <Button onClick={loadConfigs} size="sm" variant="outline">
              刷新
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="loading-spinner w-6 h-6 mx-auto mb-2" />
              <p className="text-gray-600">加载中...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {defaultConfigs.map((defaultConfig) => {
                const existingConfig = configs.find(c => c.configKey === defaultConfig.configKey);
                const currentValue = existingConfig ? existingConfig.configValue : defaultConfig.configValue;

                return (
                  <div key={defaultConfig.configKey} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium text-sm">{defaultConfig.description}</p>
                      <p className="text-xs text-gray-600">配置键: {defaultConfig.configKey}</p>
                      <p className="text-xs text-gray-600">
                        当前值: <span className="font-mono">{getConfigDisplayValue(currentValue)}</span>
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {typeof defaultConfig.configValue === 'boolean' ? (
                        <Button
                          onClick={() => handleUpdateConfig(defaultConfig.configKey, !currentValue)}
                          size="sm"
                          variant={currentValue ? 'default' : 'outline'}
                        >
                          {currentValue ? '禁用' : '启用'}
                        </Button>
                      ) : (
                        <Button
                          onClick={() => {
                            setEditingConfig({
                              id: existingConfig?.id || 0,
                              configKey: defaultConfig.configKey,
                              configValue: currentValue,
                              updatedBy: existingConfig?.updatedBy || 0,
                              updatedAt: existingConfig?.updatedAt || new Date().toISOString()
                            });
                            setEditValue(String(currentValue));
                          }}
                          size="sm"
                          variant="outline"
                        >
                          编辑
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 配置说明 */}
      <Card>
        <CardHeader>
          <CardTitle>配置说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-gray-600">
            <div>
              <p className="font-medium">新IP登录警告</p>
              <p>当用户从新IP地址登录时，显示安全警告提示</p>
            </div>
            <div>
              <p className="font-medium">API频率限制</p>
              <p>启用API请求频率限制，防止恶意攻击</p>
            </div>
            <div>
              <p className="font-medium">最大登录尝试次数</p>
              <p>用户连续登录失败的最大次数，超过后将临时锁定账户</p>
            </div>
            <div>
              <p className="font-medium">会话超时时间</p>
              <p>用户会话的有效期，超过后需要重新登录</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 编辑配置对话框 */}
      {editingConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">编辑配置</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  配置键
                </label>
                <input
                  type="text"
                  value={editingConfig.configKey}
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  配置值
                </label>
                <input
                  type={typeof editingConfig.configValue === 'number' ? 'number' : 'text'}
                  value={editValue}
                  onChange={(e) => setEditValue(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入配置值"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                onClick={() => {
                  setEditingConfig(null);
                  setEditValue('');
                }}
                variant="outline"
              >
                取消
              </Button>
              <Button
                onClick={() => {
                  let newValue: any = editValue;

                  // 根据原始类型转换值
                  if (typeof editingConfig.configValue === 'number') {
                    newValue = parseInt(editValue) || 0;
                  } else if (typeof editingConfig.configValue === 'boolean') {
                    newValue = editValue === 'true';
                  }

                  handleUpdateConfig(editingConfig.configKey, newValue);
                }}
              >
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 成功提示弹窗 */}
      {showSuccessMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-sm mx-4 shadow-lg">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {successMessage.includes('失败') ? '操作失败' : '操作成功'}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {successMessage}
              </p>
              <Button
                onClick={() => {
                  setShowSuccessMessage(false);
                  setSuccessMessage('');
                }}
                className="w-full"
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
