import { Request, Response, NextFunction } from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { prisma } from '../config/database';
import { config } from '../config/env';
import { ApiResponse, LoginRequest, RegisterRequest, LoginResponse, UserInfo, ErrorCodes } from '../types';
import { UserController } from './user.controller';
import { SystemConfigService } from '../services/system-config.service';
import { InvitationCodeService } from '../services/invitation-code.service';

export class AuthController {
  // 用户注册
  static async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { username, email, password, invitationCode }: RegisterRequest = req.body;

      // 验证输入
      if (!username || !email || !password) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '用户名、邮箱和密码不能为空',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 检查邀请码系统是否启用
      const invitationConfig = await SystemConfigService.getInvitationConfig();

      // 如果邀请码系统启用且要求邀请码，必须提供邀请码
      if (invitationConfig.enabled && invitationConfig.requireInvitationCode) {
        if (!invitationCode) {
          res.status(400).json({
            success: false,
            error: {
              code: 'INVITATION_CODE_REQUIRED',
              message: '注册需要邀请码',
              timestamp: new Date().toISOString(),
            }
          } as ApiResponse);
          return;
        }
      }

      // 如果邀请码系统启用且用户提供了邀请码，验证邀请码
      if (invitationConfig.enabled && invitationCode) {
        const validation = await InvitationCodeService.validateCode(invitationCode);
        if (!validation.isValid) {
          res.status(400).json({
            success: false,
            error: {
              code: validation.code || 'INVALID_INVITATION_CODE',
              message: validation.error || '邀请码无效',
              timestamp: new Date().toISOString(),
            }
          } as ApiResponse);
          return;
        }
      }

      // 检查用户是否已存在
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email },
            { username }
          ]
        }
      });

      if (existingUser) {
        res.status(409).json({
          success: false,
          error: {
            code: 'USER_EXISTS',
            message: existingUser.email === email ? '邮箱已被注册' : '用户名已被使用',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 密码加密
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // 创建用户
      const user = await prisma.user.create({
        data: {
          username,
          email,
          passwordHash,
          role: 'user',
          userLevel: 'free',
          status: 'active',
        },
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          userLevel: true,
          status: true,
          avatarUrl: true,
          levelExpiresAt: true,
          createdAt: true,
        }
      });

      // 生成JWT令牌
      const token = jwt.sign(
        { 
          userId: user.id, 
          email: user.email, 
          role: user.role,
          userLevel: user.userLevel 
        },
        config.jwt.secret,
        { expiresIn: config.jwt.expiresIn }
      );

      // 记录用户IP
      const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
      await AuthController.recordUserIP(user.id, clientIP, req.get('User-Agent') || '');

      // 如果使用了邀请码且邀请码系统启用，标记为已使用
      if (invitationCode && invitationConfig.enabled) {
        try {
          await InvitationCodeService.useCode(invitationCode, user.id);
        } catch (error) {
          console.error('使用邀请码失败:', error);
          // 注意：这里不抛出错误，因为用户已经创建成功
          // 可以考虑记录日志或发送通知给管理员
        }
      }

      const userInfo: UserInfo = {
        id: user.id.toString(),
        username: user.username,
        email: user.email,
        role: user.role,
        userLevel: user.userLevel,
        status: user.status,
        avatarUrl: user.avatarUrl || undefined,
        levelExpiresAt: user.levelExpiresAt?.toISOString(),
        createdAt: user.createdAt.toISOString(),
      };

      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          token,
          user: userInfo,
        }
      } as ApiResponse);

    } catch (error) {
      console.error('注册错误:', error);
      next(error);
    }
  }

  // 用户登录
  static async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email, password }: LoginRequest = req.body;

      // 验证输入
      if (!email || !password) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '邮箱和密码不能为空',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 查找用户
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          username: true,
          email: true,
          passwordHash: true,
          role: true,
          userLevel: true,
          status: true,
          avatarUrl: true,
          levelExpiresAt: true,
          createdAt: true,
        }
      });

      if (!user) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '邮箱或密码错误',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 检查用户状态
      if (user.status === 'banned') {
        res.status(403).json({
          success: false,
          error: {
            code: ErrorCodes.FORBIDDEN,
            message: '账户已被封禁，请联系管理员',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      if (user.status === 'suspended') {
        res.status(403).json({
          success: false,
          error: {
            code: ErrorCodes.FORBIDDEN,
            message: '账户已被暂停，请联系管理员',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 获取客户端信息
      const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || '';

      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
      if (!isPasswordValid) {
        // 记录登录失败历史
        await AuthController.recordLoginHistory(
          user.id,
          'password',
          false,
          '密码错误',
          clientIP,
          userAgent
        );

        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '邮箱或密码错误',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 生成JWT令牌
      const token = jwt.sign(
        { 
          userId: user.id, 
          email: user.email, 
          role: user.role,
          userLevel: user.userLevel 
        },
        config.jwt.secret,
        { expiresIn: config.jwt.expiresIn }
      );

      // 记录用户IP和检查安全性
      const ipWarning = await AuthController.checkIPSecurity(user.id, clientIP, userAgent);

      // 记录登录成功历史
      await AuthController.recordLoginHistory(
        user.id,
        'password',
        true,
        null,
        clientIP,
        userAgent
      );

      // 记录用户活动日志
      await UserController.logUserActivity(
        user.id,
        'login',
        { loginType: 'password', isSuccess: true },
        clientIP,
        userAgent
      );

      // 更新最后登录时间
      await prisma.user.update({
        where: { id: user.id },
        data: {
          lastLoginAt: new Date(),
          lastActiveAt: new Date()
        }
      });

      const userInfo: UserInfo = {
        id: user.id.toString(),
        username: user.username,
        email: user.email,
        role: user.role,
        userLevel: user.userLevel,
        status: user.status,
        avatarUrl: user.avatarUrl || undefined,
        levelExpiresAt: user.levelExpiresAt?.toISOString(),
        createdAt: user.createdAt.toISOString(),
      };

      const response: ApiResponse<{ user: any; token: string }> = {
        success: true,
        message: '登录成功',
        data: {
          user: userInfo,
          token
        },
        ...(ipWarning && { ipWarning })
      };

      res.json(response);

    } catch (error) {
      console.error('登录错误:', error);
      next(error);
    }
  }

  // 获取当前用户信息
  static async getCurrentUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          userLevel: true,
          status: true,
          avatarUrl: true,
          levelExpiresAt: true,
          createdAt: true,
        }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      const userInfo: UserInfo = {
        id: user.id.toString(),
        username: user.username,
        email: user.email,
        role: user.role,
        userLevel: user.userLevel,
        status: user.status,
        avatarUrl: user.avatarUrl || undefined,
        levelExpiresAt: user.levelExpiresAt?.toISOString(),
        createdAt: user.createdAt.toISOString(),
      };

      res.json({
        success: true,
        data: userInfo
      } as ApiResponse<UserInfo>);

    } catch (error) {
      console.error('获取用户信息错误:', error);
      next(error);
    }
  }

  // 记录用户IP
  private static async recordUserIP(userId: number, ipAddress: string, userAgent: string): Promise<void> {
    try {
      // 这里可以添加IP地理位置识别逻辑
      const existingIP = await prisma.userIpHistory.findFirst({
        where: {
          userId,
          ipAddress,
        }
      });

      if (existingIP) {
        // 更新现有IP记录
        await prisma.userIpHistory.update({
          where: { id: existingIP.id },
          data: {
            lastSeen: new Date(),
            loginCount: existingIP.loginCount + 1,
          }
        });
      } else {
        // 创建新IP记录
        await prisma.userIpHistory.create({
          data: {
            userId,
            ipAddress,
            country: 'Unknown', // 后续可以集成IP地理位置服务
            city: 'Unknown',
            isp: 'Unknown',
            firstSeen: new Date(),
            lastSeen: new Date(),
            loginCount: 1,
          }
        });
      }
    } catch (error) {
      console.error('记录用户IP失败:', error);
      // 不抛出错误，避免影响主要流程
    }
  }

  // 检查IP安全性
  private static async checkIPSecurity(userId: number, ipAddress: string, userAgent: string): Promise<any> {
    try {
      // 检查是否为新IP
      const existingIP = await prisma.userIpHistory.findFirst({
        where: {
          userId,
          ipAddress,
        }
      });

      const isNewIP = !existingIP;

      if (isNewIP && config.security.enableIpWarning) {
        // 获取用户的历史IP
        const userIPs = await prisma.userIpHistory.findMany({
          where: {
            userId,
            isTrusted: true,
          },
          orderBy: {
            lastSeen: 'desc'
          },
          take: 1
        });

        if (userIPs.length > 0 && userIPs[0]) {
          return {
            isNewIP: true,
            location: 'Unknown', // 后续可以添加地理位置信息
            lastLogin: userIPs[0]?.lastSeen.toISOString() || '',
          };
        }
      }

      return null;
    } catch (error) {
      console.error('检查IP安全性失败:', error);
      return null;
    }
  }

  // 用户登出
  static async logout(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 这里可以实现令牌黑名单逻辑
      // 目前只是返回成功响应
      res.json({
        success: true,
        message: '登出成功'
      } as ApiResponse);

    } catch (error) {
      console.error('登出错误:', error);
      next(error);
    }
  }

  // 刷新令牌
  static async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 生成新的JWT令牌
      const token = jwt.sign(
        {
          userId: parseInt(userId),
          email: req.user!.email,
          role: req.user!.role,
          userLevel: req.user!.userLevel
        },
        config.jwt.secret,
        { expiresIn: config.jwt.expiresIn }
      );

      res.json({
        success: true,
        message: '令牌刷新成功',
        data: { token }
      } as ApiResponse);

    } catch (error) {
      console.error('刷新令牌错误:', error);
      next(error);
    }
  }

  // 修改密码
  static async changePassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { currentPassword, newPassword } = req.body;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '未授权访问',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 获取用户当前密码
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        select: { passwordHash: true }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
      if (!isCurrentPasswordValid) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.UNAUTHORIZED,
            message: '当前密码错误',
            timestamp: new Date().toISOString(),
          }
        } as ApiResponse);
        return;
      }

      // 加密新密码
      const saltRounds = 12;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

      // 更新密码
      await prisma.user.update({
        where: { id: parseInt(userId) },
        data: { passwordHash: newPasswordHash }
      });

      res.json({
        success: true,
        message: '密码修改成功'
      } as ApiResponse);

    } catch (error) {
      console.error('修改密码错误:', error);
      next(error);
    }
  }

  // 忘记密码（暂时返回成功，后续可以集成邮件服务）
  static async forgotPassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.body;

      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { email },
        select: { id: true, email: true }
      });

      // 无论用户是否存在都返回成功（安全考虑）
      res.json({
        success: true,
        message: '如果该邮箱已注册，您将收到密码重置邮件'
      } as ApiResponse);

    } catch (error) {
      console.error('忘记密码错误:', error);
      next(error);
    }
  }

  // 重置密码（暂时返回错误，后续实现）
  static async resetPassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      res.status(501).json({
        success: false,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: '密码重置功能暂未实现',
          timestamp: new Date().toISOString(),
        }
      } as ApiResponse);

    } catch (error) {
      console.error('重置密码错误:', error);
      next(error);
    }
  }

  // 验证邮箱（暂时返回错误，后续实现）
  static async verifyEmail(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      res.status(501).json({
        success: false,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: '邮箱验证功能暂未实现',
          timestamp: new Date().toISOString(),
        }
      } as ApiResponse);

    } catch (error) {
      console.error('验证邮箱错误:', error);
      next(error);
    }
  }

  // 重新发送验证邮件（暂时返回错误，后续实现）
  static async resendVerification(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      res.status(501).json({
        success: false,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: '重新发送验证邮件功能暂未实现',
          timestamp: new Date().toISOString(),
        }
      } as ApiResponse);

    } catch (error) {
      console.error('重新发送验证邮件错误:', error);
      next(error);
    }
  }

  // 记录登录历史
  private static async recordLoginHistory(
    userId: number,
    loginType: string,
    isSuccess: boolean,
    failureReason?: string | null,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      // 解析设备信息
      let deviceInfo = null;
      let location = null;

      if (userAgent) {
        const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
        const browser = userAgent.match(/(Chrome|Firefox|Safari|Edge|Opera)\/[\d.]+/)?.[0] || 'Unknown';

        deviceInfo = {
          isMobile,
          browser,
          userAgent: userAgent.substring(0, 200) // 限制长度
        };
      }

      // 这里可以添加IP地理位置解析逻辑
      if (ipAddress && ipAddress !== 'unknown') {
        location = 'Unknown'; // 后续可以集成IP地理位置服务
      }

      // 创建登录历史记录
      await prisma.userLoginHistory.create({
        data: {
          userId,
          loginType,
          isSuccess,
          failureReason: failureReason || null,
          ipAddress: ipAddress || 'unknown',
          location,
          ...(deviceInfo && { deviceInfo }),
        }
      });

      console.log(`用户 ${userId} 登录历史已记录: ${isSuccess ? '成功' : '失败'}`);
    } catch (error) {
      console.error('记录登录历史失败:', error);
      // 不抛出错误，避免影响主要流程
    }
  }
}
