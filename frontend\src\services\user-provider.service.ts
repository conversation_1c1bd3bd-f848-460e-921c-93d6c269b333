import axios from 'axios';
import { API_ENDPOINTS, buildApiUrl, buildQueryString } from '../config/api';
import { getApiUrl } from '../config/api.config';
import type {
  UserProvider,
  UserProviderStats,
  GrantPermissionRequest,
  RevokePermissionRequest
} from '../types';

/**
 * 用户接口权限管理服务
 */
export class UserProviderService {
  /**
   * 获取用户可用的接口列表
   */
  static async getAvailableProviders(userId: number): Promise<UserProvider[]> {
    try {
      const url = getApiUrl(API_ENDPOINTS.USER_PROVIDERS.AVAILABLE);
      const queryString = buildQueryString({ userId });
      
      const response = await axios.get(`${url}${queryString}`, {
        timeout: 10000,
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error?.message || '获取接口列表失败');
      }
    } catch (error) {
      console.error('获取用户可用接口失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户接口使用统计
   */
  static async getUserProviderStats(userId: number, days: number = 30): Promise<UserProviderStats[]> {
    try {
      const url = getApiUrl(API_ENDPOINTS.USER_PROVIDERS.STATS);
      const queryString = buildQueryString({ userId, days });
      
      const response = await axios.get(`${url}${queryString}`, {
        timeout: 10000,
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error?.message || '获取统计数据失败');
      }
    } catch (error) {
      console.error('获取用户接口统计失败:', error);
      throw error;
    }
  }

  /**
   * 授权用户接口权限（管理员功能）
   */
  static async grantProviderPermission(request: GrantPermissionRequest): Promise<void> {
    try {
      const url = getApiUrl(API_ENDPOINTS.USER_PROVIDERS.GRANT);
      
      const response = await axios.post(url, request, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.data.success) {
        throw new Error(response.data.error?.message || '授权失败');
      }
    } catch (error) {
      console.error('授权用户接口权限失败:', error);
      throw error;
    }
  }

  /**
   * 撤销用户接口权限（管理员功能）
   */
  static async revokeProviderPermission(request: RevokePermissionRequest): Promise<void> {
    try {
      const url = getApiUrl(API_ENDPOINTS.USER_PROVIDERS.REVOKE);
      
      const response = await axios.post(url, request, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.data.success) {
        throw new Error(response.data.error?.message || '撤销权限失败');
      }
    } catch (error) {
      console.error('撤销用户接口权限失败:', error);
      throw error;
    }
  }

  /**
   * 初始化等级接口可见性配置（管理员功能）
   */
  static async initializeLevelProviderVisibility(): Promise<void> {
    try {
      const url = getApiUrl(API_ENDPOINTS.USER_PROVIDERS.INITIALIZE_VISIBILITY);
      
      const response = await axios.post(url, {}, {
        timeout: 15000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.data.success) {
        throw new Error(response.data.error?.message || '初始化配置失败');
      }
    } catch (error) {
      console.error('初始化等级接口可见性配置失败:', error);
      throw error;
    }
  }

  /**
   * 检查用户是否可以使用指定接口
   */
  static async canUseProvider(userId: number, providerId: number): Promise<boolean> {
    try {
      const providers = await this.getAvailableProviders(userId);
      return providers.some(provider => provider.id === providerId && provider.isAvailable);
    } catch (error) {
      console.error('检查用户接口权限失败:', error);
      return false;
    }
  }

  /**
   * 获取用户的高级接口列表
   */
  static async getPremiumProviders(userId: number): Promise<UserProvider[]> {
    try {
      const providers = await this.getAvailableProviders(userId);
      return providers.filter(provider => provider.isPremium && provider.isAvailable);
    } catch (error) {
      console.error('获取用户高级接口失败:', error);
      return [];
    }
  }

  /**
   * 获取用户的免费接口列表
   */
  static async getFreeProviders(userId: number): Promise<UserProvider[]> {
    try {
      const providers = await this.getAvailableProviders(userId);
      return providers.filter(provider => !provider.isPremium && provider.isAvailable);
    } catch (error) {
      console.error('获取用户免费接口失败:', error);
      return [];
    }
  }
}

export default UserProviderService;
