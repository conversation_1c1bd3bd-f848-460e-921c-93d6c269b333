#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupInvitationSystem = setupInvitationSystem;
const system_config_service_1 = require("../services/system-config.service");
const database_1 = require("../config/database");
async function setupInvitationSystem() {
    try {
        console.log('🚀 开始设置邀请码系统...');
        await database_1.prisma.$connect();
        console.log('✅ 数据库连接成功');
        console.log('📝 初始化邀请码系统配置...');
        await system_config_service_1.SystemConfigService.setInvitationConfig({
            enabled: false,
            requireInvitationCode: false,
            allowBatchGeneration: true,
            maxBatchSize: 100,
            defaultExpirationDays: null,
            autoCleanupExpired: true
        });
        console.log('✅ 邀请码系统配置初始化完成');
        const config = await system_config_service_1.SystemConfigService.getInvitationConfig();
        console.log('📋 当前邀请码系统配置:');
        console.log('   - 系统状态:', config.enabled ? '启用' : '禁用');
        console.log('   - 注册需要邀请码:', config.requireInvitationCode ? '是' : '否');
        console.log('   - 允许批量生成:', config.allowBatchGeneration ? '是' : '否');
        console.log('   - 最大批量生成数量:', config.maxBatchSize);
        console.log('   - 默认过期天数:', config.defaultExpirationDays || '永不过期');
        console.log('   - 自动清理过期码:', config.autoCleanupExpired ? '是' : '否');
        console.log('\n🎉 邀请码系统设置完成！');
        console.log('\n📌 下一步操作:');
        console.log('   1. 启动后端服务: npm run dev');
        console.log('   2. 访问管理端: http://localhost:5173/admin');
        console.log('   3. 在"邀请码管理"页面中启用系统');
        console.log('   4. 生成邀请码供用户注册使用');
    }
    catch (error) {
        console.error('❌ 邀请码系统设置失败:', error);
        process.exit(1);
    }
    finally {
        await database_1.prisma.$disconnect();
    }
}
if (require.main === module) {
    setupInvitationSystem()
        .then(() => {
        console.log('\n✨ 设置完成，退出程序');
        process.exit(0);
    })
        .catch((error) => {
        console.error('设置失败:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=setup-invitation-system.js.map