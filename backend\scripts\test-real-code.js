/**
 * 测试真实存在的邀请码
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001/api';
const REAL_CODE = 'xEHjG4Bkrasth'; // 数据库中确实存在的邀请码

async function testRealCode() {
  try {
    console.log('🧪 测试真实存在的邀请码...\n');

    // 1. 测试验证真实的邀请码
    console.log(`📡 测试验证邀请码: "${REAL_CODE}"`);
    const validateResponse = await fetch(`${BASE_URL}/admin/invitation-codes/validate/${REAL_CODE}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`📊 响应状态: ${validateResponse.status}`);
    const validateData = await validateResponse.json();
    console.log('📊 验证响应:', JSON.stringify(validateData, null, 2));

    if (validateData.success && validateData.data.isValid) {
      console.log('✅ 邀请码验证成功！');
    } else {
      console.log('❌ 邀请码验证失败');
      console.log(`💬 错误信息: ${validateData.data?.error || '未知错误'}`);
    }

    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testRealCode();
