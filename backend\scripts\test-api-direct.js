/**
 * 直接测试邀请码API
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001/api';

async function testAPI() {
  try {
    console.log('🧪 直接测试邀请码API...\n');

    // 1. 测试验证邀请码API（不需要认证）
    console.log('📡 测试验证邀请码API...');
    const validateResponse = await fetch(`${BASE_URL}/admin/invitation-codes/validate/NYaVGcw94Z7x4`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`📊 响应状态: ${validateResponse.status}`);
    const validateData = await validateResponse.json();
    console.log('📊 响应数据:', JSON.stringify(validateData, null, 2));

    // 2. 测试获取邀请码系统配置
    console.log('\n📡 测试获取邀请码系统配置...');
    const configResponse = await fetch(`${BASE_URL}/admin/invitation-codes/config`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`📊 配置响应状态: ${configResponse.status}`);
    const configData = await configResponse.json();
    console.log('📊 配置数据:', JSON.stringify(configData, null, 2));

    console.log('\n🎉 API测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testAPI();
