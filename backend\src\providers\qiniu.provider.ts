/**
 * 七牛云接口适配器
 * 基于用户提供的PHP代码实现
 */

import axios from 'axios';
import FormData from 'form-data';
import { BaseProvider, ProviderConfig, UploadResult, HealthCheckResult } from './base.provider';

export class QiniuProvider extends BaseProvider {
  constructor(config: ProviderConfig) {
    super(config);
  }

  /**
   * 上传文件到七牛云
   */
  async upload(fileBuffer: Buffer, fileName: string, mimeType: string): Promise<UploadResult> {
    const startTime = Date.now();

    try {
      // 验证文件
      const validation = this.validateFile(fileBuffer, fileName, mimeType);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
          providerId: this.config.id,
          providerName: this.config.name,
        };
      }

      // 获取七牛云token
      const tokenEndpoint = this.config.config?.tokenEndpoint || 'https://zgtdxh.kejie.org.cn/ajax/get-qiniu-token.php?prefix=user';
      
      const tokenResponse = await axios.get(tokenEndpoint, {
        timeout: 10000,
      });

      if (!tokenResponse.data || !tokenResponse.data.data || !tokenResponse.data.data.token) {
        return {
          success: false,
          error: '获取七牛云token失败',
          providerId: this.config.id,
          providerName: this.config.name,
        };
      }

      const token = tokenResponse.data.data.token;

      // 生成文件key（按照PHP代码的规则）
      const fileKey = this.generateQiniuFileKey(fileName);

      // 构建上传数据
      const formData = new FormData();
      formData.append('name', fileName);
      formData.append('chunk', '0');
      formData.append('chunks', '1');
      formData.append('key', fileKey);
      formData.append('token', token);
      formData.append('file', fileBuffer, { 
        filename: fileName, 
        contentType: mimeType 
      });

      // 上传到七牛云
      const uploadUrl = this.config.endpoint || 'https://upload.qbox.me/';
      const response = await axios.post(uploadUrl, formData, {
        headers: formData.getHeaders(),
        timeout: 30000,
      });

      const responseTime = Date.now() - startTime;

      if (response.data && response.data.key) {
        // 构建完整的访问URL
        const baseUrl = this.config.config?.baseUrl || 'https://acad-upload.scimall.org.cn/';
        const finalUrl = baseUrl + response.data.key;

        return {
          success: true,
          url: finalUrl,
          providerId: this.config.id,
          providerName: this.config.name,
          responseTime,
          metadata: {
            key: response.data.key,
            hash: response.data.hash,
            bucket: response.data.bucket,
          }
        };
      } else {
        return {
          success: false,
          error: '七牛云上传失败或响应格式错误',
          providerId: this.config.id,
          providerName: this.config.name,
          responseTime,
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        success: false,
        error: `七牛云上传失败: ${error instanceof Error ? error.message : '未知错误'}`,
        providerId: this.config.id,
        providerName: this.config.name,
        responseTime,
      };
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    try {
      // 检查token接口是否可用
      const tokenEndpoint = this.config.config?.tokenEndpoint || 'https://zgtdxh.kejie.org.cn/ajax/get-qiniu-token.php?prefix=user';
      
      const response = await axios.get(tokenEndpoint, {
        timeout: 10000,
      });

      const responseTime = Date.now() - startTime;

      if (response.data && response.data.data && response.data.data.token) {
        return {
          isHealthy: true,
          responseTime,
          lastChecked: new Date(),
        };
      } else {
        return {
          isHealthy: false,
          error: 'Token接口响应格式错误',
          responseTime,
          lastChecked: new Date(),
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        isHealthy: false,
        error: error instanceof Error ? error.message : '健康检查失败',
        responseTime,
        lastChecked: new Date(),
      };
    }
  }

  /**
   * 生成七牛云文件key（按照PHP代码的规则）
   */
  private generateQiniuFileKey(originalName: string): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    
    // 生成唯一ID
    const uniqueId = Date.now().toString() + Math.random().toString(36).substring(2, 11);
    
    // 获取文件扩展名
    const ext = originalName.split('.').pop() || 'png';
    
    // 按照PHP代码的格式：年/月/日/小时/video/唯一ID.扩展名
    return `${year}/${month}/${day}/${hour}/video/${uniqueId}.${ext}`;
  }

  /**
   * 删除文件（七牛云支持删除，但需要管理凭证）
   */
  async delete(url: string): Promise<{ success: boolean; error?: string }> {
    // 七牛云删除需要管理凭证，这里暂不实现
    // 可以根据需要添加删除功能
    return {
      success: false,
      error: '七牛云删除功能需要管理凭证，暂未实现'
    };
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(url: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 通过HEAD请求获取文件信息
      const response = await axios.head(url, {
        timeout: 10000,
      });

      return {
        success: true,
        data: {
          contentType: response.headers['content-type'],
          contentLength: response.headers['content-length'],
          lastModified: response.headers['last-modified'],
          etag: response.headers['etag'],
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取文件信息失败'
      };
    }
  }
}
