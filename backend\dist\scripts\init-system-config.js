"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initSystemConfig = initSystemConfig;
exports.checkAndUpdateSystemConfig = checkAndUpdateSystemConfig;
const system_config_service_1 = require("../services/system-config.service");
async function initSystemConfig() {
    try {
        console.log('开始初始化系统配置...');
        await system_config_service_1.SystemConfigService.setInvitationConfig({
            enabled: false,
            requireInvitationCode: false,
            allowBatchGeneration: true,
            maxBatchSize: 100,
            defaultExpirationDays: null,
            autoCleanupExpired: true
        });
        console.log('✅ 邀请码系统配置初始化完成');
        console.log('✅ 系统配置初始化完成');
    }
    catch (error) {
        console.error('❌ 系统配置初始化失败:', error);
        throw error;
    }
}
async function checkAndUpdateSystemConfig() {
    try {
        console.log('检查系统配置...');
        const invitationConfig = await system_config_service_1.SystemConfigService.getConfig(system_config_service_1.SystemConfigService.INVITATION_CONFIG_KEY);
        if (!invitationConfig) {
            console.log('邀请码系统配置不存在，正在创建...');
            await system_config_service_1.SystemConfigService.setInvitationConfig({
                enabled: false,
                requireInvitationCode: false,
                allowBatchGeneration: true,
                maxBatchSize: 100,
                defaultExpirationDays: null,
                autoCleanupExpired: true
            });
            console.log('✅ 邀请码系统配置已创建');
        }
        else {
            console.log('✅ 邀请码系统配置已存在');
        }
        console.log('✅ 系统配置检查完成');
    }
    catch (error) {
        console.error('❌ 系统配置检查失败:', error);
        throw error;
    }
}
if (require.main === module) {
    initSystemConfig()
        .then(() => {
        console.log('系统配置初始化成功');
        process.exit(0);
    })
        .catch((error) => {
        console.error('系统配置初始化失败:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=init-system-config.js.map