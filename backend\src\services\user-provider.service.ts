/**
 * 用户接口权限服务
 * 管理用户可以使用的第三方接口权限
 */

import { prisma } from '../config/database';

export interface UserProvider {
  id: number;
  name: string;
  description?: string | null;
  endpoint: string;
  status: string;
  priority: number;
  maxFileSize: number;
  supportedFormats: string[];
  requiredLevel: string;
  isPremium: boolean;
  costPerUpload: number;
  isAvailable: boolean; // 用户是否可以使用
  source: 'level' | 'manual'; // 权限来源：等级或手动授权
}

export class UserProviderService {
  /**
   * 获取用户可用的接口列表
   */
  static async getAvailableProviders(userId: number): Promise<UserProvider[]> {
    try {
      // 获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { userLevel: true }
      });

      if (!user) {
        throw new Error('用户不存在');
      }

      // 获取基于等级的可见接口
      const levelProviders = await this.getProvidersByLevel(user.userLevel);

      // 获取手动授权的接口
      const manualProviders = await this.getManuallyGrantedProviders(userId);

      // 合并并去重
      const allProviders = this.mergeProviders(levelProviders, manualProviders);

      return allProviders;
    } catch (error) {
      console.error('获取用户可用接口失败:', error);
      // 降级处理：返回所有活跃接口
      const providers = await prisma.uploadProvider.findMany({
        where: { status: 'active' },
        orderBy: { priority: 'asc' }
      });

      return providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        description: provider.description || '',
        endpoint: provider.endpoint,
        status: provider.status,
        priority: provider.priority,
        maxFileSize: Number(provider.maxFileSize),
        supportedFormats: provider.supportedFormats,
        requiredLevel: provider.requiredLevel,
        isPremium: provider.isPremium,
        costPerUpload: provider.costPerUpload ? Number(provider.costPerUpload.toString()) : 0,
        isAvailable: true,
        source: 'level' as const
      }));
    }
  }

  /**
   * 检查用户是否可以使用指定接口
   */
  static async canUseProvider(userId: number, providerId: number): Promise<boolean> {
    try {
      const availableProviders = await this.getAvailableProviders(userId);
      return availableProviders.some(provider => provider.id === providerId && provider.isAvailable);
    } catch (error) {
      console.error('检查用户接口权限失败:', error);
      return false;
    }
  }

  /**
   * 获取基于等级的可见接口
   */
  private static async getProvidersByLevel(userLevel: string): Promise<UserProvider[]> {
    try {
      const visibilityConfigs = await prisma.levelProviderVisibility.findMany({
        where: {
          level: userLevel,
          isVisible: true
        },
        include: {
          provider: true
        },
        orderBy: {
          displayOrder: 'asc'
        }
      });

      return visibilityConfigs.map(config => ({
        id: config.provider.id,
        name: config.provider.name,
        description: config.provider.description || '',
        endpoint: config.provider.endpoint,
        status: config.provider.status,
        priority: config.provider.priority,
        maxFileSize: Number(config.provider.maxFileSize),
        supportedFormats: config.provider.supportedFormats,
        requiredLevel: config.provider.requiredLevel,
        isPremium: config.provider.isPremium,
        costPerUpload: config.provider.costPerUpload ? Number(config.provider.costPerUpload.toString()) : 0,
        isAvailable: config.provider.status === 'active',
        source: 'level' as const
      })) as UserProvider[];
    } catch (error) {
      console.error('获取等级接口失败:', error);
      return [];
    }
  }

  /**
   * 获取手动授权的接口
   */
  private static async getManuallyGrantedProviders(userId: number): Promise<UserProvider[]> {
    try {
      const permissions = await prisma.userProviderPermission.findMany({
        where: {
          userId,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        include: {
          provider: true
        }
      });

      return permissions.map(permission => ({
        id: permission.provider.id,
        name: permission.provider.name,
        description: permission.provider.description || '',
        endpoint: permission.provider.endpoint,
        status: permission.provider.status,
        priority: permission.provider.priority,
        maxFileSize: Number(permission.provider.maxFileSize),
        supportedFormats: permission.provider.supportedFormats,
        requiredLevel: permission.provider.requiredLevel,
        isPremium: permission.provider.isPremium,
        costPerUpload: permission.provider.costPerUpload ? Number(permission.provider.costPerUpload.toString()) : 0,
        isAvailable: permission.provider.status === 'active',
        source: 'manual' as const
      })) as UserProvider[];
    } catch (error) {
      console.error('获取手动授权接口失败:', error);
      return [];
    }
  }

  /**
   * 合并并去重接口列表
   */
  private static mergeProviders(levelProviders: UserProvider[], manualProviders: UserProvider[]): UserProvider[] {
    const providerMap = new Map<number, UserProvider>();

    // 先添加等级接口
    levelProviders.forEach(provider => {
      providerMap.set(provider.id, provider);
    });

    // 再添加手动授权接口（会覆盖等级接口，优先级更高）
    manualProviders.forEach(provider => {
      providerMap.set(provider.id, { ...provider, source: 'manual' });
    });

    // 转换为数组并按优先级排序
    return Array.from(providerMap.values()).sort((a, b) => a.priority - b.priority);
  }

  /**
   * 为用户手动授权接口
   */
  static async grantProviderPermission(
    userId: number,
    providerId: number,
    grantedBy: number,
    expiresAt?: Date
  ): Promise<void> {
    try {
      const updateData = {
        grantedBy,
        grantedAt: new Date(),
        isActive: true,
        expiresAt: expiresAt || null
      };

      const createData = {
        userId,
        providerId,
        grantedBy,
        isActive: true,
        expiresAt: expiresAt || null
      };

      await prisma.userProviderPermission.upsert({
        where: {
          userId_providerId: {
            userId,
            providerId
          }
        },
        update: updateData as any,
        create: createData as any
      });
    } catch (error) {
      console.error('授权用户接口权限失败:', error);
      throw new Error('授权失败');
    }
  }

  /**
   * 撤销用户接口权限
   */
  static async revokeProviderPermission(userId: number, providerId: number): Promise<void> {
    try {
      await prisma.userProviderPermission.updateMany({
        where: {
          userId,
          providerId
        },
        data: {
          isActive: false
        }
      });
    } catch (error) {
      console.error('撤销用户接口权限失败:', error);
      throw new Error('撤销权限失败');
    }
  }

  /**
   * 获取用户接口使用统计
   */
  static async getUserProviderStats(userId: number, days: number = 30): Promise<any[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const stats = await prisma.uploadLog.groupBy({
        by: ['providerUsed'],
        where: {
          userId,
          createdAt: {
            gte: startDate
          },
          isSuccess: true
        },
        _count: {
          id: true
        },
        _avg: {
          uploadDuration: true
        }
      });

      // 获取接口信息
      const providerIds = stats.map(stat => stat.providerUsed).filter(id => id !== null).map(id => Number(id));
      const providers = await prisma.uploadProvider.findMany({
        where: {
          id: { in: providerIds }
        }
      });

      return stats.map(stat => {
        const provider = providers.find(p => p.id === Number(stat.providerUsed));
        return {
          providerId: stat.providerUsed,
          providerName: provider?.name || 'Unknown',
          uploadCount: stat._count.id,
          avgResponseTime: stat._avg.uploadDuration || 0
        };
      });
    } catch (error) {
      console.error('获取用户接口统计失败:', error);
      return [];
    }
  }

  /**
   * 初始化等级接口可见性配置
   */
  static async initializeLevelProviderVisibility(): Promise<void> {
    try {
      const levels = ['free', 'vip1', 'vip2', 'vip3'];
      const providers = await prisma.uploadProvider.findMany({
        orderBy: { priority: 'asc' }
      });

      for (const level of levels) {
        let visibleCount = 0;

        // 根据等级设置可见接口数量
        switch (level) {
          case 'free': visibleCount = 2; break;
          case 'vip1': visibleCount = 5; break;
          case 'vip2': visibleCount = 8; break;
          case 'vip3': visibleCount = providers.length; break;
        }

        // 为每个等级配置可见接口
        for (let i = 0; i < providers.length; i++) {
          const provider = providers[i];
          if (!provider?.id) continue;

          const isVisible = i < visibleCount;
          const providerId = provider.id; // 确保类型安全

          await prisma.levelProviderVisibility.upsert({
            where: {
              level_providerId: {
                level,
                providerId
              }
            },
            update: {
              isVisible,
              displayOrder: i
            },
            create: {
              level,
              providerId,
              isVisible,
              displayOrder: i
            }
          });
        }
      }
    } catch (error) {
      console.error('初始化等级接口可见性配置失败:', error);
      throw new Error('初始化配置失败');
    }
  }
}
