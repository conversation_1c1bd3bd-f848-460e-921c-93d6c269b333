import { Request, Response } from 'express';
export declare class AdminController {
    static getSystemStats(req: Request, res: Response): Promise<void>;
    static updateUserLevel(req: Request, res: Response): Promise<void>;
    static getUserDetails(req: Request, res: Response): Promise<void>;
    static getRealtimeLogs(req: Request, res: Response): Promise<void>;
    static getSystemLogs(req: Request, res: Response): Promise<void>;
    static getUsers(req: Request, res: Response): Promise<void>;
    static getUserDetail(req: Request, res: Response): Promise<void>;
    static updateUserStatus(req: Request, res: Response): Promise<void>;
    static updateUserProfile(req: Request, res: Response): Promise<void>;
    static getUserActivityLogs(req: Request, res: Response): Promise<void>;
    static getUserLoginHistory(req: Request, res: Response): Promise<void>;
    static getLevelApplications(req: Request, res: Response): Promise<void>;
    static processLevelApplication(req: Request, res: Response): Promise<void>;
    static deleteLevelApplication(req: Request, res: Response): Promise<void>;
    static getProviders(req: Request, res: Response): Promise<void>;
    static createProvider(req: Request, res: Response): Promise<void>;
    static updateProvider(req: Request, res: Response): Promise<void>;
    static deleteProvider(req: Request, res: Response): Promise<void>;
    static testProvider(req: Request, res: Response): Promise<void>;
    static getSystemMonitoring(req: Request, res: Response): Promise<void>;
    static getMetricsHistory(req: Request, res: Response): Promise<void>;
    static getSystemEvents(req: Request, res: Response): Promise<void>;
    static updateAlertThresholds(req: Request, res: Response): Promise<void>;
    static cleanupMonitoringData(req: Request, res: Response): Promise<void>;
    static getDataRetentionConfig(req: Request, res: Response): Promise<void>;
    static updateDataRetentionConfig(req: Request, res: Response): Promise<void>;
    static triggerDataCleanup(req: Request, res: Response): Promise<void>;
    static getIPSecurity(req: Request, res: Response): Promise<void>;
    static addIPToBlacklist(req: Request, res: Response): Promise<void>;
    static removeIPFromBlacklist(req: Request, res: Response): Promise<void>;
    static getAnalyticsOverview(req: Request, res: Response): Promise<void>;
    static getUserAnalytics(req: Request, res: Response): Promise<void>;
    static getUploadAnalytics(req: Request, res: Response): Promise<void>;
    static getRealtimeAnalytics(req: Request, res: Response): Promise<void>;
    static clearRealtimeLogs(req: Request, res: Response): Promise<void>;
    static clearSystemLogs(req: Request, res: Response): Promise<void>;
    static getIPSecurityOverview(req: Request, res: Response): Promise<void>;
    static getIPRiskLogs(req: Request, res: Response): Promise<void>;
    static getIPRiskRules(req: Request, res: Response): Promise<void>;
    static createIPRiskRule(req: Request, res: Response): Promise<void>;
    static updateIPRiskRule(req: Request, res: Response): Promise<void>;
    static deleteIPRiskRule(req: Request, res: Response): Promise<void>;
    static blockIP(req: Request, res: Response): Promise<void>;
    static unblockIP(req: Request, res: Response): Promise<void>;
    static getUserPermissionsOverview(req: Request, res: Response): Promise<void>;
    static getUserSessions(req: Request, res: Response): Promise<void>;
    static revokeUserSession(req: Request, res: Response): Promise<void>;
    static getSecurityConfigs(req: Request, res: Response): Promise<void>;
    static updateSecurityConfig(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=admin.controller.d.ts.map