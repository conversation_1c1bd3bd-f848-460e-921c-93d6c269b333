/**
 * 测试公开的邀请码验证路由
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001/api';
const REAL_CODE = 'xEHjG4Bkrasth'; // 数据库中确实存在的邀请码

async function testPublicRoute() {
  try {
    console.log('🧪 测试公开的邀请码验证路由...\n');

    // 1. 测试新的公开路由
    console.log(`📡 测试公开路由验证邀请码: "${REAL_CODE}"`);
    const validateResponse = await fetch(`${BASE_URL}/invitation-codes/validate/${REAL_CODE}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`📊 响应状态: ${validateResponse.status}`);
    const validateData = await validateResponse.json();
    console.log('📊 验证响应:', JSON.stringify(validateData, null, 2));

    if (validateData.success && validateData.data.isValid) {
      console.log('✅ 邀请码验证成功！');
    } else {
      console.log('❌ 邀请码验证失败');
      console.log(`💬 错误信息: ${validateData.data?.error || '未知错误'}`);
    }

    // 2. 测试不存在的邀请码
    console.log(`\n📡 测试不存在的邀请码: "NOTEXIST12345"`);
    const notFoundResponse = await fetch(`${BASE_URL}/invitation-codes/validate/NOTEXIST12345`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`📊 响应状态: ${notFoundResponse.status}`);
    const notFoundData = await notFoundResponse.json();
    console.log('📊 验证响应:', JSON.stringify(notFoundData, null, 2));

    if (notFoundData.success && !notFoundData.data.isValid) {
      console.log('✅ 正确识别不存在的邀请码');
    } else {
      console.log('❌ 不存在邀请码的处理有问题');
    }

    console.log('\n🎉 公开路由测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testPublicRoute();
