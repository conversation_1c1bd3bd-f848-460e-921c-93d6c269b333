/**
 * 测试邀请码API功能
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001/api/admin';
const ADMIN_TOKEN = 'test-admin-token'; // 这个需要替换为真实的管理员token

async function testInvitationAPI() {
  try {
    console.log('🧪 测试邀请码API功能...\n');

    // 1. 测试获取邀请码系统配置
    console.log('📡 测试获取邀请码系统配置...');
    const configResponse = await fetch(`${BASE_URL}/invitation-codes/config`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    const configData = await configResponse.json();
    console.log('📊 配置响应:', JSON.stringify(configData, null, 2));

    if (!configData.success) {
      console.log('❌ 获取配置失败');
      return;
    }

    // 2. 如果邀请码系统未启用，先启用它
    if (!configData.data.enabled) {
      console.log('\n📡 启用邀请码系统...');
      const enableResponse = await fetch(`${BASE_URL}/invitation-codes/config`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${ADMIN_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          enabled: true,
          requireInvitationCode: false, // 设为false，这样不会影响正常注册
          allowBatchGeneration: true,
          maxBatchSize: 100
        })
      });

      const enableData = await enableResponse.json();
      console.log('📊 启用响应:', JSON.stringify(enableData, null, 2));

      if (!enableData.success) {
        console.log('❌ 启用邀请码系统失败');
        return;
      }
    }

    // 3. 测试生成单个邀请码
    console.log('\n📡 测试生成单个邀请码...');
    const generateResponse = await fetch(`${BASE_URL}/invitation-codes/generate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        description: 'API测试邀请码'
      })
    });

    const generateData = await generateResponse.json();
    console.log('📊 生成响应:', JSON.stringify(generateData, null, 2));

    if (generateData.success) {
      console.log('✅ 邀请码生成成功');
      console.log(`📝 邀请码: ${generateData.data.code}`);
      
      // 4. 测试获取邀请码列表
      console.log('\n📡 测试获取邀请码列表...');
      const listResponse = await fetch(`${BASE_URL}/invitation-codes?page=1&limit=10`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${ADMIN_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      const listData = await listResponse.json();
      console.log('📊 列表响应:', JSON.stringify(listData, null, 2));

      if (listData.success) {
        console.log('✅ 获取邀请码列表成功');
        console.log(`📊 总数: ${listData.data.pagination.total}`);
      } else {
        console.log('❌ 获取邀请码列表失败');
      }

      // 5. 清理测试数据（删除生成的邀请码）
      console.log('\n🧹 清理测试数据...');
      const deleteResponse = await fetch(`${BASE_URL}/invitation-codes/${generateData.data.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${ADMIN_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      const deleteData = await deleteResponse.json();
      console.log('📊 删除响应:', JSON.stringify(deleteData, null, 2));

      if (deleteData.success) {
        console.log('✅ 测试数据清理完成');
      } else {
        console.log('❌ 测试数据清理失败');
      }

    } else {
      console.log('❌ 邀请码生成失败');
      console.log(`💬 错误信息: ${generateData.error?.message || '未知错误'}`);
      if (generateData.error?.stack) {
        console.log(`📚 错误堆栈: ${generateData.error.stack}`);
      }
    }

    console.log('\n🎉 邀请码API测试完成！');

  } catch (error) {
    console.log('💥 请求异常:', error.message);
    return { success: false, error: error.message };
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testInvitationAPI().catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testInvitationAPI };
