#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initLevelConfigs = initLevelConfigs;
const database_1 = require("../config/database");
async function initLevelConfigs() {
    try {
        console.log('🚀 开始初始化用户等级配置...');
        await database_1.prisma.$connect();
        console.log('✅ 数据库连接成功');
        const levelConfigs = [
            {
                level: 'free',
                displayName: '免费用户',
                maxDailyUploads: 10,
                maxFileSize: BigInt(5 * 1024 * 1024),
                maxStorageSpace: BigInt(100 * 1024 * 1024),
                canChooseProvider: false,
                prioritySupport: false,
                customDomain: false,
                visibleProviderCount: 2,
            },
            {
                level: 'vip1',
                displayName: 'VIP1用户',
                maxDailyUploads: 50,
                maxFileSize: BigInt(20 * 1024 * 1024),
                maxStorageSpace: BigInt(1024 * 1024 * 1024),
                canChooseProvider: true,
                prioritySupport: false,
                customDomain: false,
                visibleProviderCount: 4,
            },
            {
                level: 'vip2',
                displayName: 'VIP2用户',
                maxDailyUploads: 200,
                maxFileSize: BigInt(50 * 1024 * 1024),
                maxStorageSpace: BigInt(5 * 1024 * 1024 * 1024),
                canChooseProvider: true,
                prioritySupport: true,
                customDomain: false,
                visibleProviderCount: 6,
            },
            {
                level: 'vip3',
                displayName: 'VIP3用户',
                maxDailyUploads: 1000,
                maxFileSize: BigInt(100 * 1024 * 1024),
                maxStorageSpace: BigInt(20 * 1024 * 1024 * 1024),
                canChooseProvider: true,
                prioritySupport: true,
                customDomain: true,
                visibleProviderCount: 10,
            },
        ];
        console.log('📝 创建用户等级配置...');
        for (const config of levelConfigs) {
            await database_1.prisma.userLevelConfig.upsert({
                where: { level: config.level },
                update: config,
                create: config,
            });
            console.log(`✅ 创建/更新用户等级配置: ${config.displayName}`);
        }
        const allConfigs = await database_1.prisma.userLevelConfig.findMany({
            orderBy: { level: 'asc' }
        });
        console.log('\n📋 当前用户等级配置:');
        allConfigs.forEach(config => {
            console.log(`   - ${config.level}: ${config.displayName}`);
            console.log(`     每日上传限制: ${config.maxDailyUploads}`);
            console.log(`     文件大小限制: ${Number(config.maxFileSize) / (1024 * 1024)}MB`);
            console.log(`     存储空间限制: ${Number(config.maxStorageSpace) / (1024 * 1024)}MB`);
            console.log(`     可见接口数量: ${config.visibleProviderCount}`);
            console.log('');
        });
        console.log('🎉 用户等级配置初始化完成！');
    }
    catch (error) {
        console.error('❌ 用户等级配置初始化失败:', error);
        process.exit(1);
    }
    finally {
        await database_1.prisma.$disconnect();
    }
}
if (require.main === module) {
    initLevelConfigs()
        .then(() => {
        console.log('\n✨ 初始化完成，退出程序');
        process.exit(0);
    })
        .catch((error) => {
        console.error('初始化失败:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=init-level-configs.js.map