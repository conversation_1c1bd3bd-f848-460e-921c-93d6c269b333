import { Request, Response } from 'express';
import { UserProviderService } from '../services/user-provider.service';

export class UserProviderController {
  /**
   * 获取用户可用的接口列表
   */
  static async getAvailableProviders(req: Request, res: Response): Promise<void> {
    try {
      const userId = parseInt(req.query.userId as string);
      
      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PARAMS',
            message: '用户ID不能为空'
          }
        });
        return;
      }

      const providers = await UserProviderService.getAvailableProviders(userId);
      
      res.json({
        success: true,
        data: providers
      });
    } catch (error) {
      console.error('获取用户可用接口失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '获取接口列表失败'
        }
      });
    }
  }

  /**
   * 获取用户接口使用统计
   */
  static async getUserProviderStats(req: Request, res: Response): Promise<void> {
    try {
      const userId = parseInt(req.query.userId as string);
      const days = parseInt(req.query.days as string) || 30;
      
      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PARAMS',
            message: '用户ID不能为空'
          }
        });
        return;
      }

      const stats = await UserProviderService.getUserProviderStats(userId, days);
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('获取用户接口统计失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '获取统计数据失败'
        }
      });
    }
  }

  /**
   * 授权用户接口权限（管理员功能）
   */
  static async grantProviderPermission(req: Request, res: Response): Promise<void> {
    try {
      const { userId, providerId, grantedBy, expiresAt } = req.body;
      
      if (!userId || !providerId || !grantedBy) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PARAMS',
            message: '用户ID、接口ID和授权人ID不能为空'
          }
        });
        return;
      }

      await UserProviderService.grantProviderPermission(
        parseInt(userId),
        parseInt(providerId),
        parseInt(grantedBy),
        expiresAt ? new Date(expiresAt) : undefined
      );
      
      res.json({
        success: true,
        message: '授权成功'
      });
    } catch (error) {
      console.error('授权用户接口权限失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '授权失败'
        }
      });
    }
  }

  /**
   * 撤销用户接口权限（管理员功能）
   */
  static async revokeProviderPermission(req: Request, res: Response): Promise<void> {
    try {
      const { userId, providerId } = req.body;
      
      if (!userId || !providerId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PARAMS',
            message: '用户ID和接口ID不能为空'
          }
        });
        return;
      }

      await UserProviderService.revokeProviderPermission(
        parseInt(userId),
        parseInt(providerId)
      );
      
      res.json({
        success: true,
        message: '撤销权限成功'
      });
    } catch (error) {
      console.error('撤销用户接口权限失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '撤销权限失败'
        }
      });
    }
  }

  /**
   * 初始化等级接口可见性配置（管理员功能）
   */
  static async initializeLevelProviderVisibility(req: Request, res: Response): Promise<void> {
    try {
      await UserProviderService.initializeLevelProviderVisibility();
      
      res.json({
        success: true,
        message: '初始化配置成功'
      });
    } catch (error) {
      console.error('初始化等级接口可见性配置失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '初始化配置失败'
        }
      });
    }
  }
}
