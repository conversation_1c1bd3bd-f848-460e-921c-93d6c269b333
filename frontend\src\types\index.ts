export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  userLevel: string;
  status: string;
  avatarUrl?: string;
  levelExpiresAt?: string;
  createdAt: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    timestamp: string;
    requestId?: string;
  };
  message?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
  acceptPrivacy: boolean;
  invitationCode?: string; // 邀请码（可选）
}

export interface AuthResponse {
  user: User;
  token: string;
}

export interface UploadLimits {
  userLevel: string;
  maxDailyUploads: number;
  maxFileSize: number;
  maxStorageSpace: number;
  todayUploads: number;
  remainingUploads: number;
  canChooseProvider: boolean;
  visibleProviderCount: number;
}

export interface UploadHistory {
  uploads: UploadItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UploadItem {
  id: number;
  originalName: string;
  fileSize: number;
  mimeType: string;
  systemUrl: string;
  uploadStatus: string;
  isOriginalUploader: boolean;
  accessCount: number;
  createdAt: string;
  links: UploadLink[];
}

export interface UploadLink {
  provider: string;
  url: string;
  status: string;
}

export interface UploadStats {
  totalUploads: number;
  totalSize: number;
  todayUploads: number;
  thisMonthUploads: number;
}

// 接口提供商类型
export interface Provider {
  id: number;
  name: string;
  description?: string;
  endpoint: string;
  apiKey?: string;
  config?: any;
  status: string;
  priority: number;
  maxFileSize: number;
  supportedFormats: string[];
  requiredLevel: string;
  isPremium: boolean;
  costPerUpload: number;
  createdAt: string;
  updatedAt: string;
}

// 用户接口权限相关类型
export interface UserProvider {
  id: number;
  name: string;
  description: string;
  endpoint: string;
  status: string;
  priority: number;
  maxFileSize: number;
  supportedFormats: string[];
  requiredLevel: string;
  isPremium: boolean;
  costPerUpload: number;
  isAvailable: boolean;
  source: 'level' | 'manual';
  grantedBy?: number;
  grantedAt?: string;
  expiresAt?: string;
}

export interface UserProviderStats {
  providerId: number | string;
  providerName: string;
  uploadCount: number;
  avgResponseTime: number;
}

export interface GrantPermissionRequest {
  userId: number;
  providerId: number;
  grantedBy: number;
  expiresAt?: string;
}

export interface RevokePermissionRequest {
  userId: number;
  providerId: number;
}
