/**
 * 测试清除日志API功能
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001/api/admin';
const ADMIN_TOKEN = 'test-admin-token';

async function testClearLogsAPI() {
  try {
    console.log('🧪 测试清除实时日志API...\n');

    // 测试清除所有日志
    console.log('📡 测试清除所有日志...');
    const response = await fetch(`${BASE_URL}/logs/clear-realtime`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ logType: 'all' })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 清除日志API测试成功');
      console.log(`📊 响应数据:`, data.data);
      console.log(`💬 消息: ${data.data.message}`);
      console.log(`🗑️  删除数量: ${data.data.deletedCount}`);
    } else {
      console.log('❌ 清除日志API测试失败');
      console.log(`💬 错误信息:`, data.error?.message || data.message || '未知错误');
    }

    return { success: response.ok, data };
  } catch (error) {
    console.log('💥 请求异常:', error.message);
    return { success: false, error: error.message };
  }
}

async function testGetRealtimeLogs() {
  try {
    console.log('\n📡 测试获取实时日志API...');
    const response = await fetch(`${BASE_URL}/logs/realtime`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${ADMIN_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 获取实时日志API测试成功');
      console.log(`📊 日志数量: ${data.data.length}`);
      if (data.data.length > 0) {
        console.log(`📝 最新日志: ${data.data[0].type} - ${data.data[0].message}`);
      }
    } else {
      console.log('❌ 获取实时日志API测试失败');
      console.log(`💬 错误信息:`, data.error?.message || data.message || '未知错误');
    }

    return { success: response.ok, data };
  } catch (error) {
    console.log('💥 请求异常:', error.message);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 开始测试清除日志功能...\n');
  
  // 先获取当前日志状态
  const getLogsResult = await testGetRealtimeLogs();
  
  // 测试清除日志
  const clearLogsResult = await testClearLogsAPI();
  
  // 再次获取日志状态验证清除效果
  console.log('\n🔍 验证清除效果...');
  const getLogsAfterClear = await testGetRealtimeLogs();

  console.log('\n📊 测试结果汇总:');
  console.log(`✅ 获取日志API: ${getLogsResult.success ? '成功' : '失败'}`);
  console.log(`✅ 清除日志API: ${clearLogsResult.success ? '成功' : '失败'}`);
  console.log(`✅ 验证清除效果: ${getLogsAfterClear.success ? '成功' : '失败'}`);

  if (clearLogsResult.success) {
    console.log('\n🎉 清除日志功能测试通过！');
    console.log('💡 提示: 现在可以在前端管理界面中使用清除日志按钮了。');
  } else {
    console.log('\n⚠️  清除日志功能测试失败，请检查API实现。');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ 测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testClearLogsAPI, testGetRealtimeLogs, runTests };
