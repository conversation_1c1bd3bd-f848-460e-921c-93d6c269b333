import { Request, Response, NextFunction } from 'express';
import { InvitationCodeService } from '../services/invitation-code.service';
import { SystemConfigService } from '../services/system-config.service';
import { ApiResponse, ErrorCodes } from '../types';
import { LogService } from '../services/log.service';

export class InvitationCodeController {

  /**
   * 获取邀请码系统配置
   */
  static async getSystemConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const config = await SystemConfigService.getInvitationConfig();

      res.json({
        success: true,
        data: config,
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('获取邀请码系统配置失败:', error);
      next(error);
    }
  }

  /**
   * 更新邀请码系统配置
   */
  static async updateSystemConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const adminUser = (req as any).user;
      const configUpdate = req.body;

      // 记录管理员操作
      await LogService.logAdminOperation(
        Number(adminUser.id),
        'update_invitation_config',
        'system',
        undefined,
        { configUpdate },
        req.ip,
        req.get('User-Agent')
      );

      await SystemConfigService.setInvitationConfig(configUpdate);
      const newConfig = await SystemConfigService.getInvitationConfig();

      res.json({
        success: true,
        message: '邀请码系统配置已更新',
        data: newConfig,
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('更新邀请码系统配置失败:', error);
      next(error);
    }
  }

  /**
   * 生成单个邀请码
   */
  static async generateCode(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const adminUser = (req as any).user;
      const { description, expiresAt } = req.body;

      // 检查系统是否启用邀请码功能
      const config = await SystemConfigService.getInvitationConfig();
      if (!config.enabled) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVITATION_SYSTEM_DISABLED',
            message: '邀请码系统未启用',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const expirationDate = expiresAt ? new Date(expiresAt) : undefined;
      const invitationCode = await InvitationCodeService.generateCode(
        Number(adminUser.id),
        description,
        expirationDate
      );

      // 记录管理员操作
      await LogService.logAdminOperation(
        Number(adminUser.id),
        'generate_invitation_code',
        'invitation_code',
        invitationCode.id,
        { code: invitationCode.code, description, expiresAt },
        req.ip,
        req.get('User-Agent')
      );

      res.status(201).json({
        success: true,
        message: '邀请码生成成功',
        data: invitationCode,
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('生成邀请码失败:', error);
      next(error);
    }
  }

  /**
   * 批量生成邀请码
   */
  static async generateBatchCodes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const adminUser = (req as any).user;
      const { count, description, expiresAt } = req.body;

      // 验证输入
      if (!count || count < 1) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '生成数量必须大于0',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 检查系统配置
      const config = await SystemConfigService.getInvitationConfig();
      if (!config.enabled) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVITATION_SYSTEM_DISABLED',
            message: '邀请码系统未启用',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      if (!config.allowBatchGeneration) {
        res.status(400).json({
          success: false,
          error: {
            code: 'BATCH_GENERATION_DISABLED',
            message: '系统不允许批量生成邀请码',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      if (count > config.maxBatchSize) {
        res.status(400).json({
          success: false,
          error: {
            code: 'BATCH_SIZE_EXCEEDED',
            message: `批量生成数量不能超过 ${config.maxBatchSize}`,
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const expirationDate = expiresAt ? new Date(expiresAt) : undefined;
      const invitationCodes = await InvitationCodeService.generateBatchCodes(
        Number(adminUser.id),
        count,
        description,
        expirationDate
      );

      // 记录管理员操作
      await LogService.logAdminOperation(
        Number(adminUser.id),
        'generate_batch_invitation_codes',
        'invitation_code',
        undefined,
        { count, description, expiresAt, generatedCodes: invitationCodes.length },
        req.ip,
        req.get('User-Agent')
      );

      res.status(201).json({
        success: true,
        message: `成功生成 ${invitationCodes.length} 个邀请码`,
        data: {
          codes: invitationCodes,
          count: invitationCodes.length
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('批量生成邀请码失败:', error);
      next(error);
    }
  }

  /**
   * 获取邀请码列表
   */
  static async getCodesList(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        isUsed,
        createdBy,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const options: any = {
        page: parseInt(page as string),
        limit: Math.min(parseInt(limit as string), 100), // 限制最大每页数量
        isUsed: isUsed !== undefined ? isUsed === 'true' : undefined,
        createdBy: createdBy ? parseInt(createdBy as string) : undefined,
        search: search as string,
        sortBy: sortBy as 'createdAt' | 'code' | 'usedAt',
        sortOrder: sortOrder as 'asc' | 'desc'
      };

      // 移除 undefined 值
      Object.keys(options).forEach(key => {
        if (options[key] === undefined) {
          delete options[key];
        }
      });

      const result = await InvitationCodeService.getCodesList(options);

      res.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('获取邀请码列表失败:', error);
      next(error);
    }
  }

  /**
   * 验证邀请码
   */
  static async validateCode(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { code } = req.params;

      if (!code) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '邀请码不能为空',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const validation = await InvitationCodeService.validateCode(code);

      res.json({
        success: true,
        data: validation,
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('验证邀请码失败:', error);
      next(error);
    }
  }

  /**
   * 删除邀请码
   */
  static async deleteCode(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const adminUser = (req as any).user;
      const { id } = req.params;

      if (!id) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '邀请码ID不能为空',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      await InvitationCodeService.deleteCode(parseInt(id), Number(adminUser.id));

      // 记录管理员操作
      await LogService.logAdminOperation(
        Number(adminUser.id),
        'delete_invitation_code',
        'invitation_code',
        parseInt(id),
        { deletedId: id },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        message: '邀请码删除成功',
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('删除邀请码失败:', error);
      next(error);
    }
  }
  /**
   * 获取邀请码统计信息
   */
  static async getStatistics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await InvitationCodeService.getStatistics();

      res.json({
        success: true,
        data: statistics,
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('获取邀请码统计失败:', error);
      next(error);
    }
  }

  /**
   * 启用邀请码系统
   */
  static async enableSystem(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const adminUser = (req as any).user;

      await SystemConfigService.enableInvitationSystem();

      // 记录管理员操作
      await LogService.logAdminOperation(
        Number(adminUser.id),
        'enable_invitation_system',
        'system',
        undefined,
        { action: 'enable' },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        message: '邀请码系统已启用',
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('启用邀请码系统失败:', error);
      next(error);
    }
  }

  /**
   * 禁用邀请码系统
   */
  static async disableSystem(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const adminUser = (req as any).user;

      await SystemConfigService.disableInvitationSystem();

      // 记录管理员操作
      await LogService.logAdminOperation(
        Number(adminUser.id),
        'disable_invitation_system',
        'system',
        undefined,
        { action: 'disable' },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        message: '邀请码系统已禁用',
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('禁用邀请码系统失败:', error);
      next(error);
    }
  }

  /**
   * 批量删除邀请码
   */
  static async deleteBatchCodes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const adminUser = (req as any).user;
      const { ids } = req.body;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '请提供要删除的邀请码ID列表',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const result = await InvitationCodeService.deleteBatchCodes(ids, Number(adminUser.id));

      // 记录管理员操作
      await LogService.logAdminOperation(
        Number(adminUser.id),
        'delete_batch_invitation_codes',
        'invitation_code',
        undefined,
        { deletedIds: ids, result },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        message: `批量删除完成：成功 ${result.success} 个，失败 ${result.failed} 个`,
        data: result,
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('批量删除邀请码失败:', error);
      next(error);
    }
  }

  /**
   * 清理过期邀请码
   */
  static async cleanupExpiredCodes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const adminUser = (req as any).user;
      const result = await InvitationCodeService.cleanupExpiredCodes();

      // 记录管理员操作
      await LogService.logAdminOperation(
        Number(adminUser.id),
        'cleanup_expired_invitation_codes',
        'system',
        undefined,
        { deletedCount: result.deletedCount },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        message: `清理完成，删除了 ${result.deletedCount} 个过期邀请码`,
        data: result,
        timestamp: new Date().toISOString()
      } as ApiResponse);
    } catch (error) {
      console.error('清理过期邀请码失败:', error);
      next(error);
    }
  }
}
