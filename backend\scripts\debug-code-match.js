/**
 * 调试邀请码匹配问题
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugCodeMatch() {
  try {
    console.log('🔍 调试邀请码匹配问题...\n');

    // 查询所有邀请码
    const allCodes = await prisma.invitationCode.findMany({
      select: {
        id: true,
        code: true,
        isUsed: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 数据库中共有 ${allCodes.length} 个邀请码:`);
    
    allCodes.forEach((code, index) => {
      console.log(`${index + 1}. ID: ${code.id}`);
      console.log(`   Code: "${code.code}"`);
      console.log(`   Code Length: ${code.code.length}`);
      console.log(`   Code Bytes: [${Array.from(code.code).map(c => c.charCodeAt(0)).join(', ')}]`);
      console.log(`   Used: ${code.isUsed}`);
      console.log(`   Created: ${code.createdAt}`);
      console.log('');
    });

    if (allCodes.length > 0) {
      const testCode = allCodes[0].code;
      console.log(`🧪 测试查询邀请码: "${testCode}"`);
      console.log(`   测试码长度: ${testCode.length}`);
      console.log(`   测试码字节: [${Array.from(testCode).map(c => c.charCodeAt(0)).join(', ')}]`);
      
      // 尝试精确匹配
      const foundCode = await prisma.invitationCode.findUnique({
        where: { code: testCode }
      });

      if (foundCode) {
        console.log('✅ 精确匹配成功');
      } else {
        console.log('❌ 精确匹配失败');
      }

      // 尝试使用 findFirst 和 where 条件
      const foundCodeFirst = await prisma.invitationCode.findFirst({
        where: { code: testCode }
      });

      if (foundCodeFirst) {
        console.log('✅ findFirst 匹配成功');
      } else {
        console.log('❌ findFirst 匹配失败');
      }

      // 尝试使用 LIKE 查询
      const foundCodeLike = await prisma.invitationCode.findFirst({
        where: { 
          code: {
            contains: testCode
          }
        }
      });

      if (foundCodeLike) {
        console.log('✅ LIKE 匹配成功');
      } else {
        console.log('❌ LIKE 匹配失败');
      }
    }

  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行调试
debugCodeMatch();
