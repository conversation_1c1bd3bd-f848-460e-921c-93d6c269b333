"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvitationCodeController = void 0;
const invitation_code_service_1 = require("../services/invitation-code.service");
const system_config_service_1 = require("../services/system-config.service");
const types_1 = require("../types");
const log_service_1 = require("../services/log.service");
class InvitationCodeController {
    static async getSystemConfig(req, res, next) {
        try {
            const config = await system_config_service_1.SystemConfigService.getInvitationConfig();
            res.json({
                success: true,
                data: config,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取邀请码系统配置失败:', error);
            next(error);
        }
    }
    static async updateSystemConfig(req, res, next) {
        try {
            const adminUser = req.user;
            const configUpdate = req.body;
            await log_service_1.LogService.logAdminOperation(Number(adminUser.id), 'update_invitation_config', 'system', undefined, { configUpdate }, req.ip, req.get('User-Agent'));
            await system_config_service_1.SystemConfigService.setInvitationConfig(configUpdate);
            const newConfig = await system_config_service_1.SystemConfigService.getInvitationConfig();
            res.json({
                success: true,
                message: '邀请码系统配置已更新',
                data: newConfig,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('更新邀请码系统配置失败:', error);
            next(error);
        }
    }
    static async generateCode(req, res, next) {
        try {
            const adminUser = req.user;
            const { description, expiresAt } = req.body;
            const config = await system_config_service_1.SystemConfigService.getInvitationConfig();
            if (!config.enabled) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVITATION_SYSTEM_DISABLED',
                        message: '邀请码系统未启用',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const expirationDate = expiresAt ? new Date(expiresAt) : undefined;
            const invitationCode = await invitation_code_service_1.InvitationCodeService.generateCode(Number(adminUser.id), description, expirationDate);
            await log_service_1.LogService.logAdminOperation(Number(adminUser.id), 'generate_invitation_code', 'invitation_code', invitationCode.id, { code: invitationCode.code, description, expiresAt }, req.ip, req.get('User-Agent'));
            res.status(201).json({
                success: true,
                message: '邀请码生成成功',
                data: invitationCode,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('生成邀请码失败:', error);
            next(error);
        }
    }
    static async generateBatchCodes(req, res, next) {
        try {
            const adminUser = req.user;
            const { count, description, expiresAt } = req.body;
            if (!count || count < 1) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '生成数量必须大于0',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const config = await system_config_service_1.SystemConfigService.getInvitationConfig();
            if (!config.enabled) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVITATION_SYSTEM_DISABLED',
                        message: '邀请码系统未启用',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (!config.allowBatchGeneration) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'BATCH_GENERATION_DISABLED',
                        message: '系统不允许批量生成邀请码',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            if (count > config.maxBatchSize) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'BATCH_SIZE_EXCEEDED',
                        message: `批量生成数量不能超过 ${config.maxBatchSize}`,
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const expirationDate = expiresAt ? new Date(expiresAt) : undefined;
            const invitationCodes = await invitation_code_service_1.InvitationCodeService.generateBatchCodes(Number(adminUser.id), count, description, expirationDate);
            await log_service_1.LogService.logAdminOperation(Number(adminUser.id), 'generate_batch_invitation_codes', 'invitation_code', undefined, { count, description, expiresAt, generatedCodes: invitationCodes.length }, req.ip, req.get('User-Agent'));
            res.status(201).json({
                success: true,
                message: `成功生成 ${invitationCodes.length} 个邀请码`,
                data: {
                    codes: invitationCodes,
                    count: invitationCodes.length
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('批量生成邀请码失败:', error);
            next(error);
        }
    }
    static async getCodesList(req, res, next) {
        try {
            const { page = 1, limit = 20, isUsed, createdBy, search, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
            const options = {
                page: parseInt(page),
                limit: Math.min(parseInt(limit), 100),
                isUsed: isUsed !== undefined ? isUsed === 'true' : undefined,
                createdBy: createdBy ? parseInt(createdBy) : undefined,
                search: search,
                sortBy: sortBy,
                sortOrder: sortOrder
            };
            Object.keys(options).forEach(key => {
                if (options[key] === undefined) {
                    delete options[key];
                }
            });
            const result = await invitation_code_service_1.InvitationCodeService.getCodesList(options);
            res.json({
                success: true,
                data: result,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取邀请码列表失败:', error);
            next(error);
        }
    }
    static async validateCode(req, res, next) {
        try {
            const { code } = req.params;
            if (!code) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '邀请码不能为空',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const validation = await invitation_code_service_1.InvitationCodeService.validateCode(code);
            res.json({
                success: true,
                data: validation,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('验证邀请码失败:', error);
            next(error);
        }
    }
    static async deleteCode(req, res, next) {
        try {
            const adminUser = req.user;
            const { id } = req.params;
            if (!id) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '邀请码ID不能为空',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            await invitation_code_service_1.InvitationCodeService.deleteCode(parseInt(id), Number(adminUser.id));
            await log_service_1.LogService.logAdminOperation(Number(adminUser.id), 'delete_invitation_code', 'invitation_code', parseInt(id), { deletedId: id }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                message: '邀请码删除成功',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('删除邀请码失败:', error);
            next(error);
        }
    }
    static async getStatistics(req, res, next) {
        try {
            const statistics = await invitation_code_service_1.InvitationCodeService.getStatistics();
            res.json({
                success: true,
                data: statistics,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('获取邀请码统计失败:', error);
            next(error);
        }
    }
    static async enableSystem(req, res, next) {
        try {
            const adminUser = req.user;
            await system_config_service_1.SystemConfigService.enableInvitationSystem();
            await log_service_1.LogService.logAdminOperation(Number(adminUser.id), 'enable_invitation_system', 'system', undefined, { action: 'enable' }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                message: '邀请码系统已启用',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('启用邀请码系统失败:', error);
            next(error);
        }
    }
    static async disableSystem(req, res, next) {
        try {
            const adminUser = req.user;
            await system_config_service_1.SystemConfigService.disableInvitationSystem();
            await log_service_1.LogService.logAdminOperation(Number(adminUser.id), 'disable_invitation_system', 'system', undefined, { action: 'disable' }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                message: '邀请码系统已禁用',
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('禁用邀请码系统失败:', error);
            next(error);
        }
    }
    static async deleteBatchCodes(req, res, next) {
        try {
            const adminUser = req.user;
            const { ids } = req.body;
            if (!ids || !Array.isArray(ids) || ids.length === 0) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '请提供要删除的邀请码ID列表',
                        timestamp: new Date().toISOString()
                    }
                });
                return;
            }
            const result = await invitation_code_service_1.InvitationCodeService.deleteBatchCodes(ids, Number(adminUser.id));
            await log_service_1.LogService.logAdminOperation(Number(adminUser.id), 'delete_batch_invitation_codes', 'invitation_code', undefined, { deletedIds: ids, result }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                message: `批量删除完成：成功 ${result.success} 个，失败 ${result.failed} 个`,
                data: result,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('批量删除邀请码失败:', error);
            next(error);
        }
    }
    static async cleanupExpiredCodes(req, res, next) {
        try {
            const adminUser = req.user;
            const result = await invitation_code_service_1.InvitationCodeService.cleanupExpiredCodes();
            await log_service_1.LogService.logAdminOperation(Number(adminUser.id), 'cleanup_expired_invitation_codes', 'system', undefined, { deletedCount: result.deletedCount }, req.ip, req.get('User-Agent'));
            res.json({
                success: true,
                message: `清理完成，删除了 ${result.deletedCount} 个过期邀请码`,
                data: result,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('清理过期邀请码失败:', error);
            next(error);
        }
    }
}
exports.InvitationCodeController = InvitationCodeController;
//# sourceMappingURL=invitation-code.controller.js.map