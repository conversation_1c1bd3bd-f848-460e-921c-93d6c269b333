"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const database_1 = require("../config/database");
const env_1 = require("../config/env");
const types_1 = require("../types");
const user_controller_1 = require("./user.controller");
const system_config_service_1 = require("../services/system-config.service");
const invitation_code_service_1 = require("../services/invitation-code.service");
class AuthController {
    static async register(req, res, next) {
        try {
            const { username, email, password, invitationCode } = req.body;
            if (!username || !email || !password) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '用户名、邮箱和密码不能为空',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const invitationConfig = await system_config_service_1.SystemConfigService.getInvitationConfig();
            if (invitationConfig.enabled && invitationConfig.requireInvitationCode) {
                if (!invitationCode) {
                    res.status(400).json({
                        success: false,
                        error: {
                            code: 'INVITATION_CODE_REQUIRED',
                            message: '注册需要邀请码',
                            timestamp: new Date().toISOString(),
                        }
                    });
                    return;
                }
            }
            if (invitationConfig.enabled && invitationCode) {
                const validation = await invitation_code_service_1.InvitationCodeService.validateCode(invitationCode);
                if (!validation.isValid) {
                    res.status(400).json({
                        success: false,
                        error: {
                            code: validation.code || 'INVALID_INVITATION_CODE',
                            message: validation.error || '邀请码无效',
                            timestamp: new Date().toISOString(),
                        }
                    });
                    return;
                }
            }
            const existingUser = await database_1.prisma.user.findFirst({
                where: {
                    OR: [
                        { email },
                        { username }
                    ]
                }
            });
            if (existingUser) {
                res.status(409).json({
                    success: false,
                    error: {
                        code: 'USER_EXISTS',
                        message: existingUser.email === email ? '邮箱已被注册' : '用户名已被使用',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const saltRounds = 12;
            const passwordHash = await bcrypt_1.default.hash(password, saltRounds);
            const user = await database_1.prisma.user.create({
                data: {
                    username,
                    email,
                    passwordHash,
                    role: 'user',
                    userLevel: 'free',
                    status: 'active',
                },
                select: {
                    id: true,
                    username: true,
                    email: true,
                    role: true,
                    userLevel: true,
                    status: true,
                    avatarUrl: true,
                    levelExpiresAt: true,
                    createdAt: true,
                }
            });
            const token = jsonwebtoken_1.default.sign({
                userId: user.id,
                email: user.email,
                role: user.role,
                userLevel: user.userLevel
            }, env_1.config.jwt.secret, { expiresIn: env_1.config.jwt.expiresIn });
            const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
            await AuthController.recordUserIP(user.id, clientIP, req.get('User-Agent') || '');
            if (invitationCode && invitationConfig.enabled) {
                try {
                    await invitation_code_service_1.InvitationCodeService.useCode(invitationCode, user.id);
                }
                catch (error) {
                    console.error('使用邀请码失败:', error);
                }
            }
            const userInfo = {
                id: user.id.toString(),
                username: user.username,
                email: user.email,
                role: user.role,
                userLevel: user.userLevel,
                status: user.status,
                avatarUrl: user.avatarUrl || undefined,
                levelExpiresAt: user.levelExpiresAt?.toISOString(),
                createdAt: user.createdAt.toISOString(),
            };
            res.status(201).json({
                success: true,
                message: '注册成功',
                data: {
                    token,
                    user: userInfo,
                }
            });
        }
        catch (error) {
            console.error('注册错误:', error);
            next(error);
        }
    }
    static async login(req, res, next) {
        try {
            const { email, password } = req.body;
            if (!email || !password) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.INVALID_INPUT,
                        message: '邮箱和密码不能为空',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { email },
                select: {
                    id: true,
                    username: true,
                    email: true,
                    passwordHash: true,
                    role: true,
                    userLevel: true,
                    status: true,
                    avatarUrl: true,
                    levelExpiresAt: true,
                    createdAt: true,
                }
            });
            if (!user) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '邮箱或密码错误',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (user.status === 'banned') {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.FORBIDDEN,
                        message: '账户已被封禁，请联系管理员',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            if (user.status === 'suspended') {
                res.status(403).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.FORBIDDEN,
                        message: '账户已被暂停，请联系管理员',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
            const userAgent = req.get('User-Agent') || '';
            const isPasswordValid = await bcrypt_1.default.compare(password, user.passwordHash);
            if (!isPasswordValid) {
                await AuthController.recordLoginHistory(user.id, 'password', false, '密码错误', clientIP, userAgent);
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '邮箱或密码错误',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const token = jsonwebtoken_1.default.sign({
                userId: user.id,
                email: user.email,
                role: user.role,
                userLevel: user.userLevel
            }, env_1.config.jwt.secret, { expiresIn: env_1.config.jwt.expiresIn });
            const ipWarning = await AuthController.checkIPSecurity(user.id, clientIP, userAgent);
            await AuthController.recordLoginHistory(user.id, 'password', true, null, clientIP, userAgent);
            await user_controller_1.UserController.logUserActivity(user.id, 'login', { loginType: 'password', isSuccess: true }, clientIP, userAgent);
            await database_1.prisma.user.update({
                where: { id: user.id },
                data: {
                    lastLoginAt: new Date(),
                    lastActiveAt: new Date()
                }
            });
            const userInfo = {
                id: user.id.toString(),
                username: user.username,
                email: user.email,
                role: user.role,
                userLevel: user.userLevel,
                status: user.status,
                avatarUrl: user.avatarUrl || undefined,
                levelExpiresAt: user.levelExpiresAt?.toISOString(),
                createdAt: user.createdAt.toISOString(),
            };
            const response = {
                success: true,
                message: '登录成功',
                data: {
                    user: userInfo,
                    token
                },
                ...(ipWarning && { ipWarning })
            };
            res.json(response);
        }
        catch (error) {
            console.error('登录错误:', error);
            next(error);
        }
    }
    static async getCurrentUser(req, res, next) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                select: {
                    id: true,
                    username: true,
                    email: true,
                    role: true,
                    userLevel: true,
                    status: true,
                    avatarUrl: true,
                    levelExpiresAt: true,
                    createdAt: true,
                }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'USER_NOT_FOUND',
                        message: '用户不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const userInfo = {
                id: user.id.toString(),
                username: user.username,
                email: user.email,
                role: user.role,
                userLevel: user.userLevel,
                status: user.status,
                avatarUrl: user.avatarUrl || undefined,
                levelExpiresAt: user.levelExpiresAt?.toISOString(),
                createdAt: user.createdAt.toISOString(),
            };
            res.json({
                success: true,
                data: userInfo
            });
        }
        catch (error) {
            console.error('获取用户信息错误:', error);
            next(error);
        }
    }
    static async recordUserIP(userId, ipAddress, userAgent) {
        try {
            const existingIP = await database_1.prisma.userIpHistory.findFirst({
                where: {
                    userId,
                    ipAddress,
                }
            });
            if (existingIP) {
                await database_1.prisma.userIpHistory.update({
                    where: { id: existingIP.id },
                    data: {
                        lastSeen: new Date(),
                        loginCount: existingIP.loginCount + 1,
                    }
                });
            }
            else {
                await database_1.prisma.userIpHistory.create({
                    data: {
                        userId,
                        ipAddress,
                        country: 'Unknown',
                        city: 'Unknown',
                        isp: 'Unknown',
                        firstSeen: new Date(),
                        lastSeen: new Date(),
                        loginCount: 1,
                    }
                });
            }
        }
        catch (error) {
            console.error('记录用户IP失败:', error);
        }
    }
    static async checkIPSecurity(userId, ipAddress, userAgent) {
        try {
            const existingIP = await database_1.prisma.userIpHistory.findFirst({
                where: {
                    userId,
                    ipAddress,
                }
            });
            const isNewIP = !existingIP;
            if (isNewIP && env_1.config.security.enableIpWarning) {
                const userIPs = await database_1.prisma.userIpHistory.findMany({
                    where: {
                        userId,
                        isTrusted: true,
                    },
                    orderBy: {
                        lastSeen: 'desc'
                    },
                    take: 1
                });
                if (userIPs.length > 0 && userIPs[0]) {
                    return {
                        isNewIP: true,
                        location: 'Unknown',
                        lastLogin: userIPs[0]?.lastSeen.toISOString() || '',
                    };
                }
            }
            return null;
        }
        catch (error) {
            console.error('检查IP安全性失败:', error);
            return null;
        }
    }
    static async logout(req, res, next) {
        try {
            res.json({
                success: true,
                message: '登出成功'
            });
        }
        catch (error) {
            console.error('登出错误:', error);
            next(error);
        }
    }
    static async refreshToken(req, res, next) {
        try {
            const userId = req.user?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const token = jsonwebtoken_1.default.sign({
                userId: parseInt(userId),
                email: req.user.email,
                role: req.user.role,
                userLevel: req.user.userLevel
            }, env_1.config.jwt.secret, { expiresIn: env_1.config.jwt.expiresIn });
            res.json({
                success: true,
                message: '令牌刷新成功',
                data: { token }
            });
        }
        catch (error) {
            console.error('刷新令牌错误:', error);
            next(error);
        }
    }
    static async changePassword(req, res, next) {
        try {
            const userId = req.user?.id;
            const { currentPassword, newPassword } = req.body;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '未授权访问',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                select: { passwordHash: true }
            });
            if (!user) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'USER_NOT_FOUND',
                        message: '用户不存在',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const isCurrentPasswordValid = await bcrypt_1.default.compare(currentPassword, user.passwordHash);
            if (!isCurrentPasswordValid) {
                res.status(400).json({
                    success: false,
                    error: {
                        code: types_1.ErrorCodes.UNAUTHORIZED,
                        message: '当前密码错误',
                        timestamp: new Date().toISOString(),
                    }
                });
                return;
            }
            const saltRounds = 12;
            const newPasswordHash = await bcrypt_1.default.hash(newPassword, saltRounds);
            await database_1.prisma.user.update({
                where: { id: parseInt(userId) },
                data: { passwordHash: newPasswordHash }
            });
            res.json({
                success: true,
                message: '密码修改成功'
            });
        }
        catch (error) {
            console.error('修改密码错误:', error);
            next(error);
        }
    }
    static async forgotPassword(req, res, next) {
        try {
            const { email } = req.body;
            const user = await database_1.prisma.user.findUnique({
                where: { email },
                select: { id: true, email: true }
            });
            res.json({
                success: true,
                message: '如果该邮箱已注册，您将收到密码重置邮件'
            });
        }
        catch (error) {
            console.error('忘记密码错误:', error);
            next(error);
        }
    }
    static async resetPassword(req, res, next) {
        try {
            res.status(501).json({
                success: false,
                error: {
                    code: 'NOT_IMPLEMENTED',
                    message: '密码重置功能暂未实现',
                    timestamp: new Date().toISOString(),
                }
            });
        }
        catch (error) {
            console.error('重置密码错误:', error);
            next(error);
        }
    }
    static async verifyEmail(req, res, next) {
        try {
            res.status(501).json({
                success: false,
                error: {
                    code: 'NOT_IMPLEMENTED',
                    message: '邮箱验证功能暂未实现',
                    timestamp: new Date().toISOString(),
                }
            });
        }
        catch (error) {
            console.error('验证邮箱错误:', error);
            next(error);
        }
    }
    static async resendVerification(req, res, next) {
        try {
            res.status(501).json({
                success: false,
                error: {
                    code: 'NOT_IMPLEMENTED',
                    message: '重新发送验证邮件功能暂未实现',
                    timestamp: new Date().toISOString(),
                }
            });
        }
        catch (error) {
            console.error('重新发送验证邮件错误:', error);
            next(error);
        }
    }
    static async recordLoginHistory(userId, loginType, isSuccess, failureReason, ipAddress, userAgent) {
        try {
            let deviceInfo = null;
            let location = null;
            if (userAgent) {
                const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
                const browser = userAgent.match(/(Chrome|Firefox|Safari|Edge|Opera)\/[\d.]+/)?.[0] || 'Unknown';
                deviceInfo = {
                    isMobile,
                    browser,
                    userAgent: userAgent.substring(0, 200)
                };
            }
            if (ipAddress && ipAddress !== 'unknown') {
                location = 'Unknown';
            }
            await database_1.prisma.userLoginHistory.create({
                data: {
                    userId,
                    loginType,
                    isSuccess,
                    failureReason: failureReason || null,
                    ipAddress: ipAddress || 'unknown',
                    location,
                    ...(deviceInfo && { deviceInfo }),
                }
            });
            console.log(`用户 ${userId} 登录历史已记录: ${isSuccess ? '成功' : '失败'}`);
        }
        catch (error) {
            console.error('记录登录历史失败:', error);
        }
    }
}
exports.AuthController = AuthController;
//# sourceMappingURL=auth.controller.js.map