/**
 * 获取管理员token用于API测试
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001/api';

async function getAdminToken() {
  try {
    console.log('🔑 获取管理员token...\n');

    // 使用默认管理员账户登录
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    
    if (loginData.success) {
      console.log('✅ 管理员登录成功');
      console.log(`🎫 Token: ${loginData.data.token}`);
      console.log(`👤 用户: ${loginData.data.user.username}`);
      console.log(`🔰 角色: ${loginData.data.user.role}`);
      
      return loginData.data.token;
    } else {
      console.log('❌ 管理员登录失败');
      console.log(`💬 错误信息: ${loginData.error?.message || '未知错误'}`);
      return null;
    }

  } catch (error) {
    console.log('💥 请求异常:', error.message);
    return null;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  getAdminToken().then(token => {
    if (token) {
      console.log('\n📋 请将此token用于API测试:');
      console.log(`export ADMIN_TOKEN="${token}"`);
    } else {
      console.log('\n❌ 无法获取管理员token');
      process.exit(1);
    }
  }).catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { getAdminToken };
