"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvitationCodeService = void 0;
const database_1 = require("../config/database");
const system_config_service_1 = require("./system-config.service");
const crypto_1 = __importDefault(require("crypto"));
class InvitationCodeService {
    static async generateCode(createdBy, description, expiresAt) {
        try {
            const code = await this.generateUniqueCode();
            const invitationCode = await database_1.prisma.invitationCode.create({
                data: {
                    code,
                    createdBy,
                    description: description || null,
                    expiresAt: expiresAt || null
                },
                include: {
                    creator: {
                        select: {
                            id: true,
                            username: true,
                            email: true
                        }
                    }
                }
            });
            return {
                id: invitationCode.id,
                code: invitationCode.code,
                isUsed: invitationCode.isUsed,
                usedBy: invitationCode.usedBy,
                usedAt: invitationCode.usedAt,
                createdBy: invitationCode.createdBy,
                createdAt: invitationCode.createdAt,
                expiresAt: invitationCode.expiresAt,
                description: invitationCode.description,
                creator: invitationCode.creator
            };
        }
        catch (error) {
            console.error('生成邀请码失败:', error);
            if (error instanceof Error) {
                throw error;
            }
            throw new Error(`生成邀请码失败: ${error}`);
        }
    }
    static async generateBatchCodes(createdBy, count, description, expiresAt) {
        try {
            const config = await system_config_service_1.SystemConfigService.getInvitationConfig();
            if (!config.allowBatchGeneration) {
                throw new Error('系统不允许批量生成邀请码');
            }
            if (count > config.maxBatchSize) {
                throw new Error(`批量生成数量不能超过 ${config.maxBatchSize}`);
            }
            const codes = [];
            for (let i = 0; i < count; i++) {
                const code = await this.generateCode(createdBy, description, expiresAt);
                codes.push(code);
            }
            return codes;
        }
        catch (error) {
            console.error('批量生成邀请码失败:', error);
            throw error;
        }
    }
    static async validateCode(code) {
        try {
            const invitationCode = await database_1.prisma.invitationCode.findFirst({
                where: {
                    code: {
                        equals: code,
                        mode: 'insensitive'
                    }
                },
                include: {
                    creator: {
                        select: {
                            id: true,
                            username: true
                        }
                    },
                    user: {
                        select: {
                            id: true,
                            username: true,
                            email: true
                        }
                    }
                }
            });
            if (!invitationCode) {
                return {
                    isValid: false,
                    error: '邀请码不存在',
                    code: 'CODE_NOT_FOUND'
                };
            }
            if (invitationCode.isUsed) {
                return {
                    isValid: false,
                    error: '邀请码已被使用',
                    code: 'CODE_ALREADY_USED',
                    usedBy: invitationCode.user,
                    usedAt: invitationCode.usedAt
                };
            }
            if (invitationCode.expiresAt && invitationCode.expiresAt < new Date()) {
                return {
                    isValid: false,
                    error: '邀请码已过期',
                    code: 'CODE_EXPIRED',
                    expiresAt: invitationCode.expiresAt
                };
            }
            return {
                isValid: true,
                invitationCode: {
                    id: invitationCode.id,
                    code: invitationCode.code,
                    isUsed: invitationCode.isUsed,
                    usedBy: invitationCode.usedBy,
                    usedAt: invitationCode.usedAt,
                    createdBy: invitationCode.createdBy,
                    createdAt: invitationCode.createdAt,
                    expiresAt: invitationCode.expiresAt,
                    description: invitationCode.description,
                    creator: invitationCode.creator
                }
            };
        }
        catch (error) {
            console.error('验证邀请码失败:', error);
            return {
                isValid: false,
                error: '验证邀请码时发生错误',
                code: 'VALIDATION_ERROR'
            };
        }
    }
    static async useCode(code, userId) {
        try {
            const validation = await this.validateCode(code);
            if (!validation.isValid) {
                throw new Error(validation.error);
            }
            const invitationCode = await database_1.prisma.invitationCode.findFirst({
                where: {
                    code: {
                        equals: code,
                        mode: 'insensitive'
                    }
                }
            });
            if (!invitationCode) {
                throw new Error('邀请码不存在');
            }
            await database_1.prisma.invitationCode.update({
                where: { id: invitationCode.id },
                data: {
                    isUsed: true,
                    usedBy: userId,
                    usedAt: new Date()
                }
            });
            return true;
        }
        catch (error) {
            console.error('使用邀请码失败:', error);
            throw error;
        }
    }
    static async generateUniqueCode() {
        let attempts = 0;
        const maxAttempts = 10;
        while (attempts < maxAttempts) {
            const code = this.generateRandomCode();
            const existing = await database_1.prisma.invitationCode.findUnique({
                where: { code }
            });
            if (!existing) {
                return code;
            }
            attempts++;
        }
        throw new Error('生成唯一邀请码失败，请重试');
    }
    static generateRandomCode() {
        let code = '';
        const charsetLength = this.CHARSET.length;
        for (let i = 0; i < this.CODE_LENGTH; i++) {
            const randomIndex = crypto_1.default.randomInt(0, charsetLength);
            code += this.CHARSET[randomIndex];
        }
        return code;
    }
    static async getCodesList(options = {}) {
        try {
            const { page = 1, limit = 20, isUsed, createdBy, search, sortBy = 'createdAt', sortOrder = 'desc' } = options;
            const skip = (page - 1) * limit;
            const where = {};
            if (isUsed !== undefined) {
                where.isUsed = isUsed;
            }
            if (createdBy) {
                where.createdBy = createdBy;
            }
            if (search) {
                where.OR = [
                    { code: { contains: search, mode: 'insensitive' } },
                    { description: { contains: search, mode: 'insensitive' } }
                ];
            }
            const [codes, total] = await Promise.all([
                database_1.prisma.invitationCode.findMany({
                    where,
                    skip,
                    take: limit,
                    orderBy: { [sortBy]: sortOrder },
                    include: {
                        creator: {
                            select: {
                                id: true,
                                username: true,
                                email: true
                            }
                        },
                        user: {
                            select: {
                                id: true,
                                username: true,
                                email: true
                            }
                        }
                    }
                }),
                database_1.prisma.invitationCode.count({ where })
            ]);
            return {
                codes: codes.map(code => ({
                    id: code.id,
                    code: code.code,
                    isUsed: code.isUsed,
                    usedBy: code.usedBy,
                    usedAt: code.usedAt,
                    createdBy: code.createdBy,
                    createdAt: code.createdAt,
                    expiresAt: code.expiresAt,
                    description: code.description,
                    creator: code.creator,
                    user: code.user
                })),
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };
        }
        catch (error) {
            console.error('获取邀请码列表失败:', error);
            throw new Error('获取邀请码列表失败');
        }
    }
    static async deleteCode(id, adminId) {
        try {
            const invitationCode = await database_1.prisma.invitationCode.findUnique({
                where: { id }
            });
            if (!invitationCode) {
                throw new Error('邀请码不存在');
            }
            if (invitationCode.isUsed) {
                throw new Error('已使用的邀请码不能删除');
            }
            await database_1.prisma.invitationCode.delete({
                where: { id }
            });
            return true;
        }
        catch (error) {
            console.error('删除邀请码失败:', error);
            throw error;
        }
    }
    static async deleteBatchCodes(ids, adminId) {
        let success = 0;
        let failed = 0;
        for (const id of ids) {
            try {
                await this.deleteCode(id, adminId);
                success++;
            }
            catch (error) {
                console.error(`删除邀请码 ${id} 失败:`, error);
                failed++;
            }
        }
        return { success, failed };
    }
    static async cleanupExpiredCodes() {
        try {
            const result = await database_1.prisma.invitationCode.deleteMany({
                where: {
                    expiresAt: {
                        lt: new Date()
                    },
                    isUsed: false
                }
            });
            console.log(`清理了 ${result.count} 个过期的邀请码`);
            return { deletedCount: result.count };
        }
        catch (error) {
            console.error('清理过期邀请码失败:', error);
            throw error;
        }
    }
    static async getStatistics() {
        try {
            const [total, used, expired, active] = await Promise.all([
                database_1.prisma.invitationCode.count(),
                database_1.prisma.invitationCode.count({
                    where: { isUsed: true }
                }),
                database_1.prisma.invitationCode.count({
                    where: {
                        expiresAt: {
                            lt: new Date()
                        },
                        isUsed: false
                    }
                }),
                database_1.prisma.invitationCode.count({
                    where: {
                        isUsed: false,
                        OR: [
                            { expiresAt: null },
                            { expiresAt: { gt: new Date() } }
                        ]
                    }
                })
            ]);
            return {
                total,
                used,
                expired,
                active,
                usageRate: total > 0 ? Math.round((used / total) * 100) : 0
            };
        }
        catch (error) {
            console.error('获取邀请码统计失败:', error);
            throw error;
        }
    }
}
exports.InvitationCodeService = InvitationCodeService;
InvitationCodeService.CHARSET = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789abcdefghjkmnpqrstuvwxyz';
InvitationCodeService.CODE_LENGTH = 13;
//# sourceMappingURL=invitation-code.service.js.map