import type {
  User,
  ApiResponse,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  UploadLimits,
  UploadHistory,
  UploadItem,
  UploadLink,
  UploadStats,
} from '../types';
import { getApiBaseUrl, apiEndpointManager } from '../config/api.config';

const API_BASE_URL = import.meta.env.VITE_API_URL || `${getApiBaseUrl()}/api`;

class ApiClient {
  private token: string | null = null;

  constructor() {
    this.token = localStorage.getItem('auth_token');

    // 监听后端端口变化事件
    window.addEventListener('backend-port-changed', (event: CustomEvent) => {
      console.log(`🔄 API客户端检测到后端端口变化: ${event.detail.oldPort} -> ${event.detail.newPort}`);
    });
  }

  private getBaseURL(): string {
    return `${apiEndpointManager.getApiBaseUrl()}/api`;
  }

  setToken(token: string | null) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const baseURL = this.getBaseURL();
    const url = `${baseURL}${endpoint}`;

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || `HTTP ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API请求失败 (${url}):`, error);

      // 如果是连接错误，尝试切换到下一个可用端口
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.log('🔄 尝试切换到下一个可用端口...');
        await apiEndpointManager.checkAndSwitchPort();

        // 重试一次请求
        const newBaseURL = this.getBaseURL();
        if (newBaseURL !== baseURL) {
          console.log(`🔄 使用新端口重试请求: ${newBaseURL}${endpoint}`);
          const newUrl = `${newBaseURL}${endpoint}`;

          try {
            const retryResponse = await fetch(newUrl, {
              ...options,
              headers,
            });

            const retryData = await retryResponse.json();

            if (!retryResponse.ok) {
              throw new Error(retryData.error?.message || `HTTP ${retryResponse.status}`);
            }

            return retryData;
          } catch (retryError) {
            console.error(`重试请求也失败 (${newUrl}):`, retryError);
            throw retryError;
          }
        }
      }

      throw error;
    }
  }

  // 认证相关
  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    return this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    return this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async logout(): Promise<ApiResponse> {
    return this.request('/auth/logout', {
      method: 'POST',
    });
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    return this.request<User>('/auth/me');
  }

  // 上传相关
  async uploadSingle(file: File, providerId?: number): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append('file', file);

    if (providerId) {
      formData.append('providerId', providerId.toString());
    }

    return this.request('/upload/single', {
      method: 'POST',
      headers: {
        // 不设置Content-Type，让浏览器自动设置multipart/form-data
      },
      body: formData,
    });
  }

  async uploadBatch(files: File[]): Promise<ApiResponse> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    return this.request('/upload/batch', {
      method: 'POST',
      headers: {},
      body: formData,
    });
  }

  async getUploadLimits(): Promise<ApiResponse<UploadLimits>> {
    return this.request<UploadLimits>('/upload/limits');
  }

  async getUploadHistory(page = 1, limit = 20): Promise<ApiResponse<UploadHistory>> {
    return this.request<UploadHistory>(`/upload/history?page=${page}&limit=${limit}`);
  }

  async getUploadStats(): Promise<ApiResponse<UploadStats>> {
    return this.request<UploadStats>('/upload/stats');
  }

  async deleteUpload(imageId: number): Promise<ApiResponse> {
    return this.request(`/upload/${imageId}`, {
      method: 'DELETE',
    });
  }

  async retryUpload(imageId: number): Promise<ApiResponse> {
    return this.request(`/upload/retry/${imageId}`, {
      method: 'POST',
    });
  }

  async getAvailableProviders(): Promise<ApiResponse> {
    return this.request('/upload/providers');
  }
}

export const apiClient = new ApiClient();

// 导出一些便捷方法
export const auth = {
  login: (credentials: LoginRequest) => apiClient.login(credentials),
  register: (userData: RegisterRequest) => apiClient.register(userData),
  logout: () => apiClient.logout(),
  getCurrentUser: () => apiClient.getCurrentUser(),
  setToken: (token: string | null) => apiClient.setToken(token),
};

export const upload = {
  single: (file: File, providerId?: number) => apiClient.uploadSingle(file, providerId),
  batch: (files: File[]) => apiClient.uploadBatch(files),
  getLimits: () => apiClient.getUploadLimits(),
  getHistory: (page?: number, limit?: number) => apiClient.getUploadHistory(page, limit),
  getStats: () => apiClient.getUploadStats(),
  delete: (imageId: number) => apiClient.deleteUpload(imageId),
  retry: (imageId: number) => apiClient.retryUpload(imageId),
  getProviders: () => apiClient.getAvailableProviders(),
};
