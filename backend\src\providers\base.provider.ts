/**
 * 第三方接口适配器基类
 * 定义所有第三方接口必须实现的标准接口
 */

export interface ProviderConfig {
  id: number;
  name: string;
  endpoint: string;
  apiKey?: string;
  config?: any;
  maxFileSize: number;
  supportedFormats: string[];
  isPremium: boolean;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
  providerId: number;
  providerName: string;
  responseTime?: number;
  metadata?: any;
}

export interface HealthCheckResult {
  isHealthy: boolean;
  responseTime?: number;
  error?: string;
  lastChecked: Date;
}

/**
 * 第三方接口适配器基类
 */
export abstract class BaseProvider {
  protected config: ProviderConfig;

  constructor(config: ProviderConfig) {
    this.config = config;
  }

  /**
   * 获取接口名称
   */
  getName(): string {
    return this.config.name;
  }

  /**
   * 获取接口ID
   */
  getId(): number {
    return this.config.id;
  }

  /**
   * 检查文件是否符合接口要求
   */
  validateFile(fileBuffer: Buffer, fileName: string, mimeType: string): { valid: boolean; error?: string } {
    // 检查文件大小
    if (fileBuffer.length > this.config.maxFileSize) {
      return {
        valid: false,
        error: `文件大小超过限制，最大允许 ${Math.round(this.config.maxFileSize / 1024 / 1024)}MB`
      };
    }

    // 检查文件格式
    if (!this.config.supportedFormats.includes(mimeType)) {
      return {
        valid: false,
        error: `不支持的文件格式: ${mimeType}`
      };
    }

    return { valid: true };
  }

  /**
   * 上传文件到第三方接口
   * @param fileBuffer 文件缓冲区
   * @param fileName 文件名
   * @param mimeType MIME类型
   */
  abstract upload(fileBuffer: Buffer, fileName: string, mimeType: string): Promise<UploadResult>;

  /**
   * 健康检查
   */
  abstract healthCheck(): Promise<HealthCheckResult>;

  /**
   * 删除文件（如果接口支持）
   * @param url 文件URL
   */
  async delete(url: string): Promise<{ success: boolean; error?: string }> {
    // 默认实现：不支持删除
    return {
      success: false,
      error: '该接口不支持删除操作'
    };
  }

  /**
   * 获取文件信息（如果接口支持）
   * @param url 文件URL
   */
  async getFileInfo(url: string): Promise<{ success: boolean; data?: any; error?: string }> {
    // 默认实现：不支持获取文件信息
    return {
      success: false,
      error: '该接口不支持获取文件信息'
    };
  }

  /**
   * 生成唯一文件名
   */
  protected generateFileName(originalName: string): string {
    const ext = originalName.split('.').pop() || 'png';
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `${timestamp}_${random}.${ext}`;
  }

  /**
   * 生成带路径的文件名（按日期分组）
   */
  protected generateFileKey(originalName: string, prefix: string = ''): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    
    const fileName = this.generateFileName(originalName);
    
    if (prefix) {
      return `${prefix}/${year}/${month}/${day}/${hour}/${fileName}`;
    }
    
    return `${year}/${month}/${day}/${hour}/${fileName}`;
  }

  /**
   * 计算响应时间
   */
  protected async measureResponseTime<T>(operation: () => Promise<T>): Promise<{ result: T; responseTime: number }> {
    const startTime = Date.now();
    const result = await operation();
    const responseTime = Date.now() - startTime;
    
    return { result, responseTime };
  }
}
