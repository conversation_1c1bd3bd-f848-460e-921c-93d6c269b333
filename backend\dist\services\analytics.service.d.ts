export declare class AnalyticsService {
    getAnalyticsOverview(timeRange?: 'day' | 'week' | 'month' | 'year'): Promise<{
        timeRange: "year" | "week" | "day" | "month";
        period: {
            start: Date;
            end: Date;
        };
        summary: {
            users: {
                total: number;
                new: number;
                active: number;
                banned: number;
            };
            uploads: {
                total: number;
                new: number;
                successful: number;
                failed: number;
            };
            access: {
                total: number;
                uniqueVisitors: number;
                pageViews: number;
            };
            storage: {
                totalSize: number;
                totalFiles: number;
                averageSize: number;
            };
        };
        trends: {
            uploads: {
                date: string;
                count: number;
            }[];
            users: {
                date: string;
                count: number;
            }[];
            access: {
                date: string;
                count: number;
            }[];
        };
    }>;
    getUserAnalytics(timeRange?: 'day' | 'week' | 'month' | 'year'): Promise<{
        timeRange: "year" | "week" | "day" | "month";
        period: {
            start: Date;
            end: Date;
        };
        levelDistribution: {
            level: string;
            count: number;
            percentage: number;
        }[];
        activityTrends: {
            type: string;
            count: number;
        }[];
        geoDistribution: {
            location: string | null;
            count: number;
        }[];
        deviceStats: {
            mobile: number;
            desktop: number;
            browsers: Record<string, number>;
        };
        activeUsers: {
            daily: number;
            weekly: number;
            monthly: number;
        };
    }>;
    getUploadAnalytics(timeRange?: 'day' | 'week' | 'month' | 'year'): Promise<{
        timeRange: "year" | "week" | "day" | "month";
        period: {
            start: Date;
            end: Date;
        };
        trends: {
            date: string;
            count: number;
        }[];
        fileTypes: {
            type: string;
            count: number;
        }[];
        fileSizes: {
            range: string;
            count: number;
        }[];
        topUploaders: {
            userId: number;
            username: string;
            userLevel: string;
            uploadCount: number;
        }[];
        successRate: {
            successful: number;
            failed: number;
            total: number;
            successRate: number;
        };
        popularImages: {
            imageId: number;
            fileName: string;
            fileSize: number;
            mimeType: string;
            systemUrl: string;
            accessCount: number;
            uploader: {
                username: string;
                level: string;
            };
            totalAccess: number;
        }[];
    }>;
    private getUserStats;
    private getUploadStats;
    private getAccessStats;
    private getStorageStats;
    private getUserLevelDistribution;
    private getUserActivityTrends;
    private getUserGeoDistribution;
    private getUserDeviceStats;
    private getActiveUserStats;
    private getStartDate;
    private getUploadTrends;
    private getFileTypeDistribution;
    private getFileSizeDistribution;
    private getTopUploaders;
    private getUploadSuccessRate;
    private getPopularImages;
    private getTrendData;
    private getDateFormat;
    getRealtimeStats(): Promise<{
        uploads: {
            today: number;
            yesterday: number;
            growth: number;
        };
        users: {
            online: number;
            newToday: number;
        };
        system: {
            cpu: number;
            memory: number;
            disk: number;
        };
    }>;
    private getOnlineUsersCount;
    private getSystemLoadStats;
}
//# sourceMappingURL=analytics.service.d.ts.map