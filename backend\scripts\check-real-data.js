/**
 * 检查数据库中的真实数据
 * 用于验证数据分析功能的数据源
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkRealData() {
  try {
    console.log('🔍 检查数据库中的真实数据...\n');

    // 检查用户数据
    const userCount = await prisma.user.count();
    const users = await prisma.user.findMany({
      take: 5,
      select: {
        id: true,
        username: true,
        userLevel: true,
        status: true,
        createdAt: true,
        lastActiveAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`👥 用户数据:`);
    console.log(`   总用户数: ${userCount}`);
    console.log(`   最近用户:`, users.map(u => `${u.username} (${u.userLevel})`).join(', '));

    // 检查图片数据
    const imageCount = await prisma.image.count();
    const images = await prisma.image.findMany({
      take: 5,
      select: {
        id: true,
        originalName: true,
        fileSize: true,
        mimeType: true,
        createdAt: true,
        isDeleted: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`\n📷 图片数据:`);
    console.log(`   总图片数: ${imageCount}`);
    console.log(`   最近图片:`, images.map(i => `${i.originalName} (${(Number(i.fileSize) / 1024 / 1024).toFixed(2)}MB)`).join(', '));

    // 检查用户图片关联
    const userImageCount = await prisma.userImage.count();
    const userImages = await prisma.userImage.findMany({
      take: 5,
      include: {
        user: { select: { username: true } },
        image: { select: { originalName: true } }
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`\n🔗 用户图片关联:`);
    console.log(`   总关联数: ${userImageCount}`);
    console.log(`   最近关联:`, userImages.map(ui => `${ui.user.username} -> ${ui.image.originalName}`).join(', '));

    // 检查上传日志
    const uploadLogCount = await prisma.uploadLog.count();
    const uploadLogs = await prisma.uploadLog.findMany({
      take: 5,
      select: {
        id: true,
        actionType: true,
        isSuccess: true,
        fileName: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`\n📤 上传日志:`);
    console.log(`   总日志数: ${uploadLogCount}`);
    console.log(`   最近日志:`, uploadLogs.map(l => `${l.actionType} (${l.isSuccess ? '成功' : '失败'})`).join(', '));

    // 检查访问日志
    const accessLogCount = await prisma.accessLog.count();
    const accessLogs = await prisma.accessLog.findMany({
      take: 5,
      select: {
        id: true,
        ipAddress: true,
        country: true,
        city: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`\n👁️ 访问日志:`);
    console.log(`   总访问数: ${accessLogCount}`);
    console.log(`   最近访问:`, accessLogs.map(l => `${l.ipAddress} (${l.country || 'Unknown'})`).join(', '));

    // 检查用户活动日志
    const activityLogCount = await prisma.userActivityLog.count();
    const activityLogs = await prisma.userActivityLog.findMany({
      take: 5,
      select: {
        id: true,
        activityType: true,
        location: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`\n📊 用户活动日志:`);
    console.log(`   总活动数: ${activityLogCount}`);
    console.log(`   最近活动:`, activityLogs.map(l => `${l.activityType} (${l.location || 'Unknown'})`).join(', '));

    // 检查系统指标
    const systemMetricCount = await prisma.systemMetric.count();
    const systemMetrics = await prisma.systemMetric.findMany({
      take: 5,
      select: {
        id: true,
        metricType: true,
        metricValue: true,
        unit: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`\n📈 系统指标:`);
    console.log(`   总指标数: ${systemMetricCount}`);
    console.log(`   最近指标:`, systemMetrics.map(m => `${m.metricType}: ${Number(m.metricValue).toFixed(2)}${m.unit}`).join(', '));

    // 数据完整性检查
    console.log(`\n🔍 数据完整性检查:`);
    
    const hasUsers = userCount > 0;
    const hasImages = imageCount > 0;
    const hasUserImages = userImageCount > 0;
    const hasUploadLogs = uploadLogCount > 0;
    const hasAccessLogs = accessLogCount > 0;
    const hasActivityLogs = activityLogCount > 0;
    const hasSystemMetrics = systemMetricCount > 0;

    console.log(`   ✅ 用户数据: ${hasUsers ? '有数据' : '❌ 无数据'}`);
    console.log(`   ✅ 图片数据: ${hasImages ? '有数据' : '❌ 无数据'}`);
    console.log(`   ✅ 用户图片关联: ${hasUserImages ? '有数据' : '❌ 无数据'}`);
    console.log(`   ✅ 上传日志: ${hasUploadLogs ? '有数据' : '❌ 无数据'}`);
    console.log(`   ✅ 访问日志: ${hasAccessLogs ? '有数据' : '❌ 无数据'}`);
    console.log(`   ✅ 用户活动日志: ${hasActivityLogs ? '有数据' : '❌ 无数据'}`);
    console.log(`   ✅ 系统指标: ${hasSystemMetrics ? '有数据' : '❌ 无数据'}`);

    const dataCompleteness = [hasUsers, hasImages, hasUserImages, hasUploadLogs, hasAccessLogs, hasActivityLogs, hasSystemMetrics].filter(Boolean).length;
    const totalChecks = 7;
    
    console.log(`\n📊 数据完整性: ${dataCompleteness}/${totalChecks} (${((dataCompleteness / totalChecks) * 100).toFixed(1)}%)`);

    if (dataCompleteness === totalChecks) {
      console.log(`\n🎉 数据库包含完整的真实数据，数据分析功能可以正常工作！`);
    } else if (dataCompleteness >= 4) {
      console.log(`\n⚠️  数据库包含部分真实数据，数据分析功能可以部分工作。`);
    } else {
      console.log(`\n❌ 数据库缺少关键数据，建议先使用系统产生真实数据。`);
    }

  } catch (error) {
    console.error('❌ 检查真实数据失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkRealData().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { checkRealData };
