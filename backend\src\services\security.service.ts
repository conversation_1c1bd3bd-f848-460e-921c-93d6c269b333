import { prisma } from '../config/database';
import { Prisma } from '@prisma/client';

/**
 * 安全管理服务类
 * 提供IP风控、用户权限、安全配置等核心安全功能
 */
export class SecurityService {

  /**
   * 获取IP风控概览
   */
  async getIPSecurityOverview() {
    const [
      totalIPs,
      blockedIPs,
      riskIPs,
      recentActivity,
      activeRules
    ] = await Promise.all([
      this.getTotalUniqueIPs(),
      this.getBlockedIPsCount(),
      this.getRiskIPsCount(),
      this.getRecentIPActivity(),
      this.getActiveRiskRules()
    ]);

    return {
      summary: {
        totalIPs,
        blockedIPs,
        riskIPs,
        activeRules: activeRules.length
      },
      recentActivity,
      activeRules: activeRules.slice(0, 5) // 只返回前5个规则
    };
  }

  /**
   * 获取IP风控日志列表
   */
  async getIPRiskLogs(options: {
    page?: number;
    limit?: number;
    ipAddress?: string;
    actionType?: string;
    isBlocked?: boolean;
    startDate?: Date;
    endDate?: Date;
  } = {}) {
    const {
      page = 1,
      limit = 20,
      ipAddress,
      actionType,
      isBlocked,
      startDate,
      endDate
    } = options;

    const where: Prisma.IpRiskLogWhereInput = {};

    if (ipAddress) {
      where.ipAddress = { contains: ipAddress };
    }

    if (actionType) {
      where.actionType = actionType;
    }

    if (typeof isBlocked === 'boolean') {
      where.isBlocked = isBlocked;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = startDate;
      if (endDate) where.createdAt.lte = endDate;
    }

    const [logs, total] = await Promise.all([
      prisma.ipRiskLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              userLevel: true
            }
          },
          rule: {
            select: {
              id: true,
              ruleName: true,
              ruleType: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.ipRiskLog.count({ where })
    ]);

    return {
      logs,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 获取IP风控规则列表
   */
  async getIPRiskRules() {
    const rules = await prisma.ipRiskRule.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        _count: {
          select: {
            ipRiskLogs: {
              where: {
                createdAt: {
                  gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
                }
              }
            }
          }
        }
      }
    });

    return rules.map(rule => ({
      ...rule,
      recentTriggers: rule._count.ipRiskLogs
    }));
  }

  /**
   * 创建IP风控规则
   */
  async createIPRiskRule(data: {
    ruleName: string;
    ruleType: string;
    timeWindow: number;
    maxAttempts: number;
    blockDuration: number;
    isActive?: boolean;
  }) {
    return await prisma.ipRiskRule.create({
      data: {
        ...data,
        isActive: data.isActive ?? true
      }
    });
  }

  /**
   * 更新IP风控规则
   */
  async updateIPRiskRule(id: number, data: {
    ruleName?: string;
    ruleType?: string;
    timeWindow?: number;
    maxAttempts?: number;
    blockDuration?: number;
    isActive?: boolean;
  }) {
    return await prisma.ipRiskRule.update({
      where: { id },
      data
    });
  }

  /**
   * 删除IP风控规则
   */
  async deleteIPRiskRule(id: number) {
    return await prisma.ipRiskRule.delete({
      where: { id }
    });
  }

  /**
   * 手动封禁IP
   */
  async blockIP(data: {
    ipAddress: string;
    blockDuration: number; // 分钟
    reason: string;
    adminId: number;
  }) {
    const blockExpiresAt = new Date();
    blockExpiresAt.setMinutes(blockExpiresAt.getMinutes() + data.blockDuration);

    return await prisma.ipRiskLog.create({
      data: {
        ipAddress: data.ipAddress,
        actionType: 'manual_block',
        isBlocked: true,
        blockExpiresAt,
        riskScore: 100,
        userAgent: `Manual block by admin ${data.adminId}: ${data.reason}`
      }
    });
  }

  /**
   * 解封IP
   */
  async unblockIP(ipAddress: string, adminId: number) {
    // 将该IP的所有封禁记录设为过期
    await prisma.ipRiskLog.updateMany({
      where: {
        ipAddress,
        isBlocked: true,
        blockExpiresAt: {
          gt: new Date()
        }
      },
      data: {
        blockExpiresAt: new Date() // 设为当前时间，立即过期
      }
    });

    // 记录解封操作
    return await prisma.ipRiskLog.create({
      data: {
        ipAddress,
        actionType: 'manual_unblock',
        isBlocked: false,
        riskScore: 0,
        userAgent: `Manual unblock by admin ${adminId}`
      }
    });
  }

  /**
   * 获取用户权限概览
   */
  async getUserPermissionsOverview() {
    const [
      totalUsers,
      adminUsers,
      vipUsers,
      bannedUsers,
      recentPermissionChanges
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { role: 'admin' } }),
      prisma.user.count({ where: { userLevel: { not: 'free' } } }),
      prisma.user.count({ where: { status: 'banned' } }),
      this.getRecentPermissionChanges()
    ]);

    return {
      summary: {
        totalUsers,
        adminUsers,
        vipUsers,
        bannedUsers
      },
      recentChanges: recentPermissionChanges
    };
  }

  /**
   * 获取用户会话列表
   */
  async getUserSessions(options: {
    page?: number;
    limit?: number;
    userId?: number;
    ipAddress?: string;
    isActive?: boolean;
  } = {}) {
    const {
      page = 1,
      limit = 20,
      userId,
      ipAddress,
      isActive
    } = options;

    const where: Prisma.UserSessionWhereInput = {};

    if (userId) {
      where.userId = userId;
    }

    if (ipAddress) {
      where.ipAddress = { contains: ipAddress };
    }

    if (typeof isActive === 'boolean') {
      if (isActive) {
        where.expiresAt = { gt: new Date() };
      } else {
        where.expiresAt = { lte: new Date() };
      }
    }

    const [sessions, total] = await Promise.all([
      prisma.userSession.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              userLevel: true,
              role: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.userSession.count({ where })
    ]);

    return {
      sessions: sessions.map(session => ({
        ...session,
        isActive: session.expiresAt > new Date()
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 强制注销用户会话
   */
  async revokeUserSession(sessionId: number, adminId: number) {
    const session = await prisma.userSession.update({
      where: { id: sessionId },
      data: {
        expiresAt: new Date() // 设为当前时间，立即过期
      },
      include: {
        user: {
          select: { username: true }
        }
      }
    });

    // 记录操作日志
    await this.logSecurityAction(adminId, 'revoke_session', {
      sessionId,
      targetUser: session.user.username,
      ipAddress: session.ipAddress
    });

    return session;
  }

  /**
   * 获取安全配置
   */
  async getSecurityConfigs() {
    return await prisma.securityConfig.findMany({
      orderBy: { configKey: 'asc' }
    });
  }

  /**
   * 更新安全配置
   */
  async updateSecurityConfig(configKey: string, configValue: any, adminId: number) {
    const config = await prisma.securityConfig.upsert({
      where: { configKey },
      update: {
        configValue,
        updatedBy: adminId,
        updatedAt: new Date()
      },
      create: {
        configKey,
        configValue,
        updatedBy: adminId
      }
    });

    // 记录操作日志
    await this.logSecurityAction(adminId, 'update_security_config', {
      configKey,
      configValue
    });

    return config;
  }

  // 私有辅助方法
  private async getTotalUniqueIPs(): Promise<number> {
    const result = await prisma.ipRiskLog.groupBy({
      by: ['ipAddress'],
      _count: true
    });
    return result.length;
  }

  private async getBlockedIPsCount(): Promise<number> {
    const result = await prisma.ipRiskLog.groupBy({
      by: ['ipAddress'],
      where: {
        isBlocked: true,
        blockExpiresAt: { gt: new Date() }
      }
    });
    return result.length;
  }

  private async getRiskIPsCount(): Promise<number> {
    const result = await prisma.ipRiskLog.groupBy({
      by: ['ipAddress'],
      where: {
        riskScore: { gte: 50 },
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
        }
      }
    });
    return result.length;
  }

  private async getRecentIPActivity() {
    return await prisma.ipRiskLog.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { username: true }
        }
      }
    });
  }

  private async getActiveRiskRules() {
    return await prisma.ipRiskRule.findMany({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    });
  }

  private async getRecentPermissionChanges() {
    return await prisma.userLevelHistory.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { username: true }
        }
      }
    });
  }

  private async logSecurityAction(adminId: number, action: string, details: any) {
    // 这里可以记录到专门的安全操作日志表
    // 暂时使用现有的 AdminOperationLog
    await prisma.adminOperationLog.create({
      data: {
        adminId,
        operationType: action,
        targetType: 'security',
        operationDetails: details
      }
    });
  }
}
