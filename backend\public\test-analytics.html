<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析API测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 数据分析API测试工具</h1>
        <p>测试LoftChat数据分析功能的API接口</p>

        <div class="test-section">
            <h3>📊 数据分析概览</h3>
            <button class="test-button" onclick="testAPI('/api/admin/analytics/overview?timeRange=month', 'overview-result')">
                测试概览API (月度)
            </button>
            <button class="test-button" onclick="testAPI('/api/admin/analytics/overview?timeRange=week', 'overview-result')">
                测试概览API (周度)
            </button>
            <div id="overview-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>👥 用户行为分析</h3>
            <button class="test-button" onclick="testAPI('/api/admin/analytics/users?timeRange=month', 'users-result')">
                测试用户分析API
            </button>
            <div id="users-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📁 上传数据分析</h3>
            <button class="test-button" onclick="testAPI('/api/admin/analytics/uploads?timeRange=month', 'uploads-result')">
                测试上传分析API
            </button>
            <div id="uploads-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>⚡ 实时统计数据</h3>
            <button class="test-button" onclick="testAPI('/api/admin/analytics/realtime', 'realtime-result')">
                测试实时数据API
            </button>
            <div id="realtime-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔄 批量测试</h3>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <div id="batch-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function testAPI(endpoint, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🔄 正在测试...';

            try {
                const response = await fetch(endpoint, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer test-admin-token',
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功 (${response.status})\n\n` + 
                        `数据结构: ${Object.keys(data.data || {}).join(', ')}\n\n` +
                        `响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 失败 (${response.status})\n\n` +
                        `错误信息: ${data.error?.message || data.message || '未知错误'}\n\n` +
                        `完整响应:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `💥 请求异常: ${error.message}`;
            }
        }

        async function runAllTests() {
            const resultDiv = document.getElementById('batch-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🚀 开始批量测试...\n';

            const tests = [
                { endpoint: '/api/admin/analytics/overview?timeRange=month', name: '数据概览(月度)' },
                { endpoint: '/api/admin/analytics/users?timeRange=month', name: '用户分析' },
                { endpoint: '/api/admin/analytics/uploads?timeRange=month', name: '上传分析' },
                { endpoint: '/api/admin/analytics/realtime', name: '实时数据' }
            ];

            let results = [];
            let successCount = 0;

            for (const test of tests) {
                try {
                    const response = await fetch(test.endpoint, {
                        method: 'GET',
                        headers: {
                            'Authorization': 'Bearer test-admin-token',
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        results.push(`✅ ${test.name}: 成功 (${response.status})`);
                        successCount++;
                    } else {
                        const data = await response.json();
                        results.push(`❌ ${test.name}: 失败 (${response.status}) - ${data.error?.message || '未知错误'}`);
                    }
                } catch (error) {
                    results.push(`💥 ${test.name}: 异常 - ${error.message}`);
                }

                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            resultDiv.className = successCount === tests.length ? 'result success' : 'result error';
            resultDiv.textContent = `📊 批量测试完成\n\n` +
                `成功: ${successCount}/${tests.length}\n` +
                `成功率: ${((successCount / tests.length) * 100).toFixed(1)}%\n\n` +
                `详细结果:\n${results.join('\n')}`;
        }

        // 页面加载完成后自动运行一次测试
        window.addEventListener('load', () => {
            console.log('🔬 数据分析API测试工具已加载');
        });
    </script>
</body>
</html>
