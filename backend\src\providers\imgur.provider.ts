/**
 * Imgur接口适配器
 */

import axios from 'axios';
import FormData from 'form-data';
import { BaseProvider, ProviderConfig, UploadResult, HealthCheckResult } from './base.provider';

export class ImgurProvider extends BaseProvider {
  constructor(config: ProviderConfig) {
    super(config);
  }

  /**
   * 上传文件到Imgur
   */
  async upload(fileBuffer: Buffer, fileName: string, mimeType: string): Promise<UploadResult> {
    const startTime = Date.now();

    try {
      // 验证文件
      const validation = this.validateFile(fileBuffer, fileName, mimeType);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
          providerId: this.config.id,
          providerName: this.config.name,
        };
      }

      // 构建上传数据
      const formData = new FormData();
      formData.append('image', fileBuffer, {
        filename: fileName,
        contentType: mimeType
      });
      formData.append('type', 'file');
      formData.append('title', fileName);

      // 上传到Imgur
      const response = await axios.post('https://api.imgur.com/3/image', formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Client-ID ${this.config.apiKey}`,
        },
        timeout: 30000,
      });

      const responseTime = Date.now() - startTime;

      if (response.data && response.data.success && response.data.data) {
        const data = response.data.data;
        
        return {
          success: true,
          url: data.link,
          providerId: this.config.id,
          providerName: this.config.name,
          responseTime,
          metadata: {
            id: data.id,
            deletehash: data.deletehash,
            width: data.width,
            height: data.height,
            size: data.size,
          }
        };
      } else {
        return {
          success: false,
          error: 'Imgur上传失败或响应格式错误',
          providerId: this.config.id,
          providerName: this.config.name,
          responseTime,
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      let errorMessage = 'Imgur上传失败';
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 403) {
          errorMessage = 'Imgur API密钥无效或已过期';
        } else if (error.response?.status === 429) {
          errorMessage = 'Imgur API请求频率超限';
        } else if (error.response?.data?.data?.error) {
          errorMessage = `Imgur错误: ${error.response.data.data.error}`;
        }
      }
      
      return {
        success: false,
        error: errorMessage,
        providerId: this.config.id,
        providerName: this.config.name,
        responseTime,
      };
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    try {
      // 检查API是否可用（获取账户信息）
      const response = await axios.get('https://api.imgur.com/3/account/me', {
        headers: {
          'Authorization': `Client-ID ${this.config.apiKey}`,
        },
        timeout: 10000,
      });

      const responseTime = Date.now() - startTime;

      if (response.data && response.data.success) {
        return {
          isHealthy: true,
          responseTime,
          lastChecked: new Date(),
        };
      } else {
        return {
          isHealthy: false,
          error: 'Imgur API响应异常',
          responseTime,
          lastChecked: new Date(),
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      let errorMessage = 'Imgur健康检查失败';
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 403) {
          errorMessage = 'Imgur API密钥无效';
        } else if (error.response?.status === 429) {
          errorMessage = 'Imgur API请求频率超限';
        }
      }
      
      return {
        isHealthy: false,
        error: errorMessage,
        responseTime,
        lastChecked: new Date(),
      };
    }
  }

  /**
   * 删除文件
   */
  async delete(url: string): Promise<{ success: boolean; error?: string }> {
    try {
      // 从URL中提取图片ID
      const imageId = this.extractImageIdFromUrl(url);
      if (!imageId) {
        return {
          success: false,
          error: '无法从URL中提取图片ID'
        };
      }

      // 注意：删除需要deletehash，这里只是示例
      // 实际使用时需要在上传时保存deletehash
      const response = await axios.delete(`https://api.imgur.com/3/image/${imageId}`, {
        headers: {
          'Authorization': `Client-ID ${this.config.apiKey}`,
        },
        timeout: 10000,
      });

      if (response.data && response.data.success) {
        return { success: true };
      } else {
        return {
          success: false,
          error: 'Imgur删除失败'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Imgur删除失败'
      };
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(url: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const imageId = this.extractImageIdFromUrl(url);
      if (!imageId) {
        return {
          success: false,
          error: '无法从URL中提取图片ID'
        };
      }

      const response = await axios.get(`https://api.imgur.com/3/image/${imageId}`, {
        headers: {
          'Authorization': `Client-ID ${this.config.apiKey}`,
        },
        timeout: 10000,
      });

      if (response.data && response.data.success && response.data.data) {
        const data = response.data.data;
        return {
          success: true,
          data: {
            id: data.id,
            title: data.title,
            description: data.description,
            width: data.width,
            height: data.height,
            size: data.size,
            type: data.type,
            views: data.views,
            datetime: data.datetime,
          }
        };
      } else {
        return {
          success: false,
          error: 'Imgur获取文件信息失败'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Imgur获取文件信息失败'
      };
    }
  }

  /**
   * 从URL中提取图片ID
   */
  private extractImageIdFromUrl(url: string): string | null {
    // Imgur URL格式: https://i.imgur.com/{id}.{ext}
    const match = url.match(/imgur\.com\/([a-zA-Z0-9]+)/);
    return match ? match[1] : null;
  }
}
