import { prisma } from '../config/database';
import { SystemConfigService } from './system-config.service';
import crypto from 'crypto';

/**
 * 邀请码服务
 * 负责邀请码的生成、验证、管理等功能
 */
export class InvitationCodeService {
  
  // 邀请码字符集：大小写字母 + 数字，排除容易混淆的字符
  private static readonly CHARSET = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789abcdefghjkmnpqrstuvwxyz';
  private static readonly CODE_LENGTH = 13;

  /**
   * 生成单个邀请码
   * @param createdBy 创建者用户ID
   * @param description 描述信息
   * @param expiresAt 过期时间
   * @returns 生成的邀请码信息
   */
  static async generateCode(
    createdBy: number,
    description?: string,
    expiresAt?: Date
  ): Promise<InvitationCodeInfo> {
    try {
      // 生成唯一的邀请码
      const code = await this.generateUniqueCode();
      
      // 创建邀请码记录
      const invitationCode = await prisma.invitationCode.create({
        data: {
          code,
          createdBy,
          description: description || null,
          expiresAt: expiresAt || null
        },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              email: true
            }
          }
        }
      });

      return {
        id: invitationCode.id,
        code: invitationCode.code,
        isUsed: invitationCode.isUsed,
        usedBy: invitationCode.usedBy,
        usedAt: invitationCode.usedAt,
        createdBy: invitationCode.createdBy,
        createdAt: invitationCode.createdAt,
        expiresAt: invitationCode.expiresAt,
        description: invitationCode.description,
        creator: invitationCode.creator
      };
    } catch (error) {
      console.error('生成邀请码失败:', error);
      // 保留原始错误信息
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`生成邀请码失败: ${error}`);
    }
  }

  /**
   * 批量生成邀请码
   * @param createdBy 创建者用户ID
   * @param count 生成数量
   * @param description 描述信息
   * @param expiresAt 过期时间
   * @returns 生成的邀请码列表
   */
  static async generateBatchCodes(
    createdBy: number,
    count: number,
    description?: string,
    expiresAt?: Date
  ): Promise<InvitationCodeInfo[]> {
    try {
      // 检查批量生成配置
      const config = await SystemConfigService.getInvitationConfig();
      if (!config.allowBatchGeneration) {
        throw new Error('系统不允许批量生成邀请码');
      }

      if (count > config.maxBatchSize) {
        throw new Error(`批量生成数量不能超过 ${config.maxBatchSize}`);
      }

      const codes: InvitationCodeInfo[] = [];
      
      // 批量生成邀请码
      for (let i = 0; i < count; i++) {
        const code = await this.generateCode(createdBy, description, expiresAt);
        codes.push(code);
      }

      return codes;
    } catch (error) {
      console.error('批量生成邀请码失败:', error);
      throw error;
    }
  }

  /**
   * 验证邀请码
   * @param code 邀请码
   * @returns 验证结果
   */
  static async validateCode(code: string): Promise<InvitationCodeValidationResult> {
    try {
      // 查找邀请码（大小写不敏感）
      const invitationCode = await prisma.invitationCode.findFirst({
        where: {
          code: {
            equals: code,
            mode: 'insensitive'
          }
        },
        include: {
          creator: {
            select: {
              id: true,
              username: true
            }
          },
          user: {
            select: {
              id: true,
              username: true,
              email: true
            }
          }
        }
      });

      if (!invitationCode) {
        return {
          isValid: false,
          error: '邀请码不存在',
          code: 'CODE_NOT_FOUND'
        };
      }

      // 检查是否已使用
      if (invitationCode.isUsed) {
        return {
          isValid: false,
          error: '邀请码已被使用',
          code: 'CODE_ALREADY_USED',
          usedBy: invitationCode.user,
          usedAt: invitationCode.usedAt
        };
      }

      // 检查是否过期
      if (invitationCode.expiresAt && invitationCode.expiresAt < new Date()) {
        return {
          isValid: false,
          error: '邀请码已过期',
          code: 'CODE_EXPIRED',
          expiresAt: invitationCode.expiresAt
        };
      }

      return {
        isValid: true,
        invitationCode: {
          id: invitationCode.id,
          code: invitationCode.code,
          isUsed: invitationCode.isUsed,
          usedBy: invitationCode.usedBy,
          usedAt: invitationCode.usedAt,
          createdBy: invitationCode.createdBy,
          createdAt: invitationCode.createdAt,
          expiresAt: invitationCode.expiresAt,
          description: invitationCode.description,
          creator: invitationCode.creator
        }
      };
    } catch (error) {
      console.error('验证邀请码失败:', error);
      return {
        isValid: false,
        error: '验证邀请码时发生错误',
        code: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * 使用邀请码
   * @param code 邀请码
   * @param userId 使用者用户ID
   * @returns 使用结果
   */
  static async useCode(code: string, userId: number): Promise<boolean> {
    try {
      // 先验证邀请码
      const validation = await this.validateCode(code);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // 先找到邀请码记录（大小写不敏感）
      const invitationCode = await prisma.invitationCode.findFirst({
        where: {
          code: {
            equals: code,
            mode: 'insensitive'
          }
        }
      });

      if (!invitationCode) {
        throw new Error('邀请码不存在');
      }

      // 标记邀请码为已使用
      await prisma.invitationCode.update({
        where: { id: invitationCode.id },
        data: {
          isUsed: true,
          usedBy: userId,
          usedAt: new Date()
        }
      });

      return true;
    } catch (error) {
      console.error('使用邀请码失败:', error);
      throw error;
    }
  }

  /**
   * 生成唯一的邀请码
   * @returns 唯一的邀请码字符串
   */
  private static async generateUniqueCode(): Promise<string> {
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      const code = this.generateRandomCode();
      
      // 检查是否已存在
      const existing = await prisma.invitationCode.findUnique({
        where: { code }
      });

      if (!existing) {
        return code;
      }

      attempts++;
    }

    throw new Error('生成唯一邀请码失败，请重试');
  }

  /**
   * 生成随机邀请码
   * @returns 13位随机邀请码
   */
  private static generateRandomCode(): string {
    let code = '';
    const charsetLength = this.CHARSET.length;
    
    for (let i = 0; i < this.CODE_LENGTH; i++) {
      const randomIndex = crypto.randomInt(0, charsetLength);
      code += this.CHARSET[randomIndex];
    }
    
    return code;
  }

  /**
   * 获取邀请码列表
   * @param options 查询选项
   * @returns 邀请码列表和分页信息
   */
  static async getCodesList(options: GetCodesListOptions = {}): Promise<InvitationCodesListResult> {
    try {
      const {
        page = 1,
        limit = 20,
        isUsed,
        createdBy,
        search,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const skip = (page - 1) * limit;
      
      // 构建查询条件
      const where: any = {};
      
      if (isUsed !== undefined) {
        where.isUsed = isUsed;
      }
      
      if (createdBy) {
        where.createdBy = createdBy;
      }
      
      if (search) {
        where.OR = [
          { code: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ];
      }

      // 查询邀请码列表
      const [codes, total] = await Promise.all([
        prisma.invitationCode.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            creator: {
              select: {
                id: true,
                username: true,
                email: true
              }
            },
            user: {
              select: {
                id: true,
                username: true,
                email: true
              }
            }
          }
        }),
        prisma.invitationCode.count({ where })
      ]);

      return {
        codes: codes.map(code => ({
          id: code.id,
          code: code.code,
          isUsed: code.isUsed,
          usedBy: code.usedBy,
          usedAt: code.usedAt,
          createdBy: code.createdBy,
          createdAt: code.createdAt,
          expiresAt: code.expiresAt,
          description: code.description,
          creator: code.creator,
          user: code.user
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      console.error('获取邀请码列表失败:', error);
      throw new Error('获取邀请码列表失败');
    }
  }

  /**
   * 删除邀请码
   * @param id 邀请码ID
   * @param adminId 操作管理员ID
   * @returns 删除结果
   */
  static async deleteCode(id: number, adminId: number): Promise<boolean> {
    try {
      // 检查邀请码是否存在
      const invitationCode = await prisma.invitationCode.findUnique({
        where: { id }
      });

      if (!invitationCode) {
        throw new Error('邀请码不存在');
      }

      // 如果邀请码已被使用，不允许删除
      if (invitationCode.isUsed) {
        throw new Error('已使用的邀请码不能删除');
      }

      // 删除邀请码
      await prisma.invitationCode.delete({
        where: { id }
      });

      return true;
    } catch (error) {
      console.error('删除邀请码失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除邀请码
   * @param ids 邀请码ID列表
   * @param adminId 操作管理员ID
   * @returns 删除结果
   */
  static async deleteBatchCodes(ids: number[], adminId: number): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const id of ids) {
      try {
        await this.deleteCode(id, adminId);
        success++;
      } catch (error) {
        console.error(`删除邀请码 ${id} 失败:`, error);
        failed++;
      }
    }

    return { success, failed };
  }

  /**
   * 清理过期的邀请码
   * @returns 清理结果
   */
  static async cleanupExpiredCodes(): Promise<{ deletedCount: number }> {
    try {
      const result = await prisma.invitationCode.deleteMany({
        where: {
          expiresAt: {
            lt: new Date()
          },
          isUsed: false
        }
      });

      console.log(`清理了 ${result.count} 个过期的邀请码`);
      return { deletedCount: result.count };
    } catch (error) {
      console.error('清理过期邀请码失败:', error);
      throw error;
    }
  }

  /**
   * 获取邀请码统计信息
   * @returns 统计信息
   */
  static async getStatistics(): Promise<InvitationCodeStatistics> {
    try {
      const [total, used, expired, active] = await Promise.all([
        // 总数
        prisma.invitationCode.count(),
        // 已使用
        prisma.invitationCode.count({
          where: { isUsed: true }
        }),
        // 已过期
        prisma.invitationCode.count({
          where: {
            expiresAt: {
              lt: new Date()
            },
            isUsed: false
          }
        }),
        // 可用的
        prisma.invitationCode.count({
          where: {
            isUsed: false,
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } }
            ]
          }
        })
      ]);

      return {
        total,
        used,
        expired,
        active,
        usageRate: total > 0 ? Math.round((used / total) * 100) : 0
      };
    } catch (error) {
      console.error('获取邀请码统计失败:', error);
      throw error;
    }
  }
}

// 类型定义
export interface InvitationCodeInfo {
  id: number;
  code: string;
  isUsed: boolean;
  usedBy: number | null;
  usedAt: Date | null;
  createdBy: number;
  createdAt: Date;
  expiresAt: Date | null;
  description: string | null;
  creator: {
    id: number;
    username: string;
    email?: string;
  };
  user?: {
    id: number;
    username: string;
    email: string;
  } | null;
}

export interface InvitationCodeValidationResult {
  isValid: boolean;
  error?: string;
  code?: string;
  invitationCode?: InvitationCodeInfo;
  usedBy?: {
    id: number;
    username: string;
    email: string;
  } | null;
  usedAt?: Date | null;
  expiresAt?: Date | null;
}

export interface GetCodesListOptions {
  page?: number;
  limit?: number;
  isUsed?: boolean;
  createdBy?: number;
  search?: string;
  sortBy?: 'createdAt' | 'code' | 'usedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface InvitationCodesListResult {
  codes: InvitationCodeInfo[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface InvitationCodeStatistics {
  total: number;
  used: number;
  expired: number;
  active: number;
  usageRate: number; // 使用率百分比
}
