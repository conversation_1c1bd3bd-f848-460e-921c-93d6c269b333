import React, { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { <PERSON>, CardHeader, <PERSON>T<PERSON>le, CardContent, CardFooter } from '../ui/Card';
import { useAuth } from '../../contexts/AuthContext';
import { InvitationService } from '../../services/invitation-code.service';
import type { InvitationSystemConfig } from '../../services/invitation-code.service';

interface RegisterFormProps {
  onSwitchToLogin: () => void;
}

export function RegisterForm({ onSwitchToLogin }: RegisterFormProps) {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [acceptPrivacy, setAcceptPrivacy] = useState(false);
  const [invitationCode, setInvitationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [invitationConfig, setInvitationConfig] = useState<InvitationSystemConfig | null>(null);
  const [configLoading, setConfigLoading] = useState(true);
  const [invitationCodeValidation, setInvitationCodeValidation] = useState<{
    isValid: boolean;
    message: string;
    isValidating: boolean;
  }>({ isValid: false, message: '', isValidating: false });

  const { register } = useAuth();

  // 获取邀请码系统配置
  useEffect(() => {
    const loadInvitationConfig = async () => {
      try {
        setConfigLoading(true);
        const config = await InvitationService.getSystemConfig();
        setInvitationConfig(config);
      } catch (error) {
        console.error('获取邀请码配置失败:', error);
        // 设置默认配置（关闭状态）
        setInvitationConfig({
          enabled: false,
          requireInvitationCode: false,
          allowBatchGeneration: true,
          maxBatchSize: 100,
          defaultExpirationDays: null,
          autoCleanupExpired: true
        });
      } finally {
        setConfigLoading(false);
      }
    };

    loadInvitationConfig();
  }, []);

  // 验证邀请码
  useEffect(() => {
    const validateInvitationCode = async () => {
      if (!invitationCode || !invitationConfig?.enabled) {
        setInvitationCodeValidation({ isValid: false, message: '', isValidating: false });
        return;
      }

      if (!InvitationService.validateInvitationCodeFormat(invitationCode)) {
        setInvitationCodeValidation({
          isValid: false,
          message: '邀请码格式不正确，应为13位字母和数字',
          isValidating: false
        });
        return;
      }

      setInvitationCodeValidation({ isValid: false, message: '', isValidating: true });

      try {
        const validation = await InvitationService.validateInvitationCode(invitationCode);
        if (validation.isValid) {
          setInvitationCodeValidation({
            isValid: true,
            message: '邀请码有效',
            isValidating: false
          });
        } else {
          setInvitationCodeValidation({
            isValid: false,
            message: validation.error || '邀请码无效',
            isValidating: false
          });
        }
      } catch (error) {
        setInvitationCodeValidation({
          isValid: false,
          message: '验证邀请码时出错',
          isValidating: false
        });
      }
    };

    const timeoutId = setTimeout(validateInvitationCode, 500); // 防抖
    return () => clearTimeout(timeoutId);
  }, [invitationCode, invitationConfig]);

  // 密码强度检查
  const checkPasswordStrength = (pwd: string) => {
    const hasLower = /[a-z]/.test(pwd);
    const hasUpper = /[A-Z]/.test(pwd);
    const hasNumber = /\d/.test(pwd);
    const isLongEnough = pwd.length >= 8;

    return {
      hasLower,
      hasUpper,
      hasNumber,
      isLongEnough,
      isValid: hasLower && hasUpper && hasNumber && isLongEnough
    };
  };

  const passwordStrength = checkPasswordStrength(password);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!passwordStrength.isValid) {
      setError('密码不符合要求，请检查密码规则');
      return;
    }

    if (password !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    if (!acceptTerms) {
      setError('请同意服务条款');
      return;
    }

    if (!acceptPrivacy) {
      setError('请同意隐私政策');
      return;
    }

    // 检查邀请码
    if (invitationConfig?.enabled && invitationConfig?.requireInvitationCode) {
      if (!invitationCode) {
        setError('注册需要邀请码');
        return;
      }

      if (!InvitationService.validateInvitationCodeFormat(invitationCode)) {
        setError('邀请码格式不正确，应为13位字母和数字');
        return;
      }
    }

    setLoading(true);

    try {
      await register(
        username,
        email,
        password,
        confirmPassword,
        acceptTerms,
        acceptPrivacy,
        invitationCode || undefined
      );
    } catch (error) {
      setError(error instanceof Error ? error.message : '注册失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-gray-900">
          注册 LoftChat 账号
        </CardTitle>
        <p className="text-gray-600 mt-2">
          创建您的图片管理账号
        </p>
      </CardHeader>
      
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {error && (
            <div className="bg-error-50 border border-error-200 text-error-700 px-4 py-3 rounded-md">
              {error}
            </div>
          )}
          
          <Input
            label="用户名"
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="请输入用户名"
            required
          />
          
          <Input
            label="邮箱地址"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="请输入邮箱地址"
            required
          />
          
          <div>
            <Input
              label="密码"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="请输入密码（至少8位）"
              required
            />
            {password && (
              <div className="mt-2 space-y-1 text-sm">
                <div className="text-gray-600 font-medium">密码要求：</div>
                <div className={`flex items-center space-x-2 ${passwordStrength.isLongEnough ? 'text-green-600' : 'text-gray-400'}`}>
                  <span>{passwordStrength.isLongEnough ? '✓' : '○'}</span>
                  <span>至少8个字符</span>
                </div>
                <div className={`flex items-center space-x-2 ${passwordStrength.hasLower ? 'text-green-600' : 'text-gray-400'}`}>
                  <span>{passwordStrength.hasLower ? '✓' : '○'}</span>
                  <span>包含小写字母</span>
                </div>
                <div className={`flex items-center space-x-2 ${passwordStrength.hasUpper ? 'text-green-600' : 'text-gray-400'}`}>
                  <span>{passwordStrength.hasUpper ? '✓' : '○'}</span>
                  <span>包含大写字母</span>
                </div>
                <div className={`flex items-center space-x-2 ${passwordStrength.hasNumber ? 'text-green-600' : 'text-gray-400'}`}>
                  <span>{passwordStrength.hasNumber ? '✓' : '○'}</span>
                  <span>包含数字</span>
                </div>
              </div>
            )}
          </div>
          
          <Input
            label="确认密码"
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="请再次输入密码"
            required
          />

          {/* 邀请码输入框 - 根据系统配置显示/隐藏 */}
          {!configLoading && invitationConfig?.enabled && (
            <div>
              <Input
                label={`邀请码${invitationConfig?.requireInvitationCode ? ' *' : ' (可选)'}`}
                type="text"
                value={invitationCode}
                onChange={(e) => setInvitationCode(e.target.value.toUpperCase())}
                placeholder="请输入13位邀请码"
                required={invitationConfig?.requireInvitationCode}
                maxLength={13}
                className={
                  invitationCode && !invitationCodeValidation.isValidating
                    ? invitationCodeValidation.isValid
                      ? 'border-green-500 focus:border-green-500'
                      : 'border-red-500 focus:border-red-500'
                    : ''
                }
              />
              <div className="mt-1 text-xs">
                {invitationCodeValidation.isValidating ? (
                  <span className="text-blue-500">验证中...</span>
                ) : invitationCodeValidation.message ? (
                  <span className={invitationCodeValidation.isValid ? 'text-green-600' : 'text-red-600'}>
                    {invitationCodeValidation.message}
                  </span>
                ) : (
                  <span className="text-gray-500">
                    请输入13位邀请码（字母和数字组合）
                    {invitationConfig?.requireInvitationCode ? '' : '，可选填'}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* 服务条款和隐私政策 */}
          <div className="space-y-3 pt-2">
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="acceptTerms"
                checked={acceptTerms}
                onChange={(e) => setAcceptTerms(e.target.checked)}
                className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="acceptTerms" className="text-sm text-gray-700">
                我已阅读并同意{' '}
                <a href="/terms" target="_blank" className="text-primary-600 hover:text-primary-700 underline">
                  服务条款
                </a>
              </label>
            </div>

            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="acceptPrivacy"
                checked={acceptPrivacy}
                onChange={(e) => setAcceptPrivacy(e.target.checked)}
                className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="acceptPrivacy" className="text-sm text-gray-700">
                我已阅读并同意{' '}
                <a href="/privacy" target="_blank" className="text-primary-600 hover:text-primary-700 underline">
                  隐私政策
                </a>
              </label>
            </div>
          </div>
        </CardContent>
        
        <CardFooter className="flex flex-col space-y-4">
          <Button
            type="submit"
            className="w-full"
            loading={loading}
            disabled={
              configLoading ||
              !username ||
              !email ||
              !passwordStrength.isValid ||
              !confirmPassword ||
              !acceptTerms ||
              !acceptPrivacy ||
              password !== confirmPassword ||
              (invitationConfig?.enabled && invitationConfig?.requireInvitationCode && !invitationCode)
            }
          >
            注册
          </Button>
          
          <div className="text-center text-sm text-gray-600">
            已有账号？{' '}
            <button
              type="button"
              onClick={onSwitchToLogin}
              className="text-primary-600 hover:text-primary-700 font-medium"
            >
              立即登录
            </button>
          </div>
        </CardFooter>
      </form>
    </Card>
  );
}
