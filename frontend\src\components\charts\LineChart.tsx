import React from 'react';
import {
  LineChart as Recharts<PERSON><PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';

interface DataPoint {
  [key: string]: any;
}

interface LineChartProps {
  data: DataPoint[];
  lines: Array<{
    dataKey: string;
    color: string;
    name: string;
  }>;
  xAxisKey: string;
  title?: string;
  height?: number;
  showGrid?: boolean;
  showTooltip?: boolean;
  formatXAxis?: (value: any) => string;
  formatTooltip?: (value: any, name: string) => [string, string];
  className?: string;
}

const CustomTooltip: React.FC<any> = ({ active, payload, label, formatTooltip }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm text-gray-600 mb-2">{label}</p>
        {payload.map((entry, index) => {
          const [formattedValue, formattedName] = formatTooltip 
            ? formatTooltip(entry.value, entry.name)
            : [entry.value, entry.name];
          
          return (
            <p key={index} className="text-sm font-medium" style={{ color: entry.color }}>
              {formattedName}: {formattedValue}
            </p>
          );
        })}
      </div>
    );
  }
  return null;
};

export function LineChart({
  data,
  lines,
  xAxisKey,
  title,
  height = 300,
  showGrid = true,
  showTooltip = true,
  formatXAxis,
  formatTooltip,
  className = ''
}: LineChartProps) {
  return (
    <div className={`w-full ${className}`}>
      {title && (
        <h3 className="text-sm font-medium text-gray-900 mb-4">{title}</h3>
      )}
      <div style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <RechartsLineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
            <XAxis
              dataKey={xAxisKey}
              tickFormatter={formatXAxis}
              fontSize={12}
              stroke="#6b7280"
              axisLine={false}
              tickLine={false}
            />
            <YAxis
              fontSize={12}
              stroke="#6b7280"
              axisLine={false}
              tickLine={false}
            />
            {showTooltip && (
              <Tooltip
                content={<CustomTooltip formatTooltip={formatTooltip} />}
              />
            )}
            {lines.map((line, index) => (
              <Line
                key={index}
                type="monotone"
                dataKey={line.dataKey}
                stroke={line.color}
                strokeWidth={2}
                dot={{ fill: line.color, strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: line.color, strokeWidth: 2 }}
              />
            ))}
          </RechartsLineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
