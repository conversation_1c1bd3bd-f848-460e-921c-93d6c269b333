{"version": 3, "file": "init-system-config.js", "sourceRoot": "", "sources": ["../../src/scripts/init-system-config.ts"], "names": [], "mappings": ";;AAmFS,4CAAgB;AAAE,gEAA0B;AAnFrD,6EAAwE;AAOxE,KAAK,UAAU,gBAAgB;IAC7B,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAG5B,MAAM,2CAAmB,CAAC,mBAAmB,CAAC;YAC5C,OAAO,EAAE,KAAK;YACd,qBAAqB,EAAE,KAAK;YAC5B,oBAAoB,EAAE,IAAI;YAC1B,YAAY,EAAE,GAAG;YACjB,qBAAqB,EAAE,IAAI;YAC3B,kBAAkB,EAAE,IAAI;SACzB,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAU9B,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC7B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACrC,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAMD,KAAK,UAAU,0BAA0B;IACvC,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAGzB,MAAM,gBAAgB,GAAG,MAAM,2CAAmB,CAAC,SAAS,CAAC,2CAAmB,CAAC,qBAAqB,CAAC,CAAC;QACxG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAClC,MAAM,2CAAmB,CAAC,mBAAmB,CAAC;gBAC5C,OAAO,EAAE,KAAK;gBACd,qBAAqB,EAAE,KAAK;gBAC5B,oBAAoB,EAAE,IAAI;gBAC1B,YAAY,EAAE,GAAG;gBACjB,qBAAqB,EAAE,IAAI;gBAC3B,kBAAkB,EAAE,IAAI;aACzB,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC5B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACpC,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,gBAAgB,EAAE;SACf,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACnC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}