/**
 * 启用邀请码系统
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function enableInvitationSystem() {
  try {
    console.log('🔧 启用邀请码系统...\n');

    // 检查是否已存在配置
    const existingConfig = await prisma.systemConfig.findUnique({
      where: { key: 'invitation_system' }
    });

    const invitationConfig = {
      enabled: true,
      requireInvitationCode: false, // 设为false，这样不会影响正常注册，但如果提供邀请码会验证
      allowBatchGeneration: true,
      maxBatchSize: 100,
      defaultExpirationDays: null, // null 表示永不过期
      autoCleanupExpired: true
    };

    if (existingConfig) {
      // 更新现有配置
      await prisma.systemConfig.update({
        where: { key: 'invitation_system' },
        data: {
          value: invitationConfig,
          updatedAt: new Date()
        }
      });
      console.log('✅ 邀请码系统配置已更新');
    } else {
      // 创建新配置
      await prisma.systemConfig.create({
        data: {
          key: 'invitation_system',
          value: invitationConfig,
          description: '邀请码系统配置'
        }
      });
      console.log('✅ 邀请码系统配置已创建');
    }

    console.log('📋 当前配置:');
    console.log(JSON.stringify(invitationConfig, null, 2));

    console.log('\n🎉 邀请码系统已启用！');
    console.log('💡 提示: 现在可以通过管理端生成邀请码了');

  } catch (error) {
    console.error('❌ 启用邀请码系统失败:', error);
    console.error('错误详情:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
enableInvitationSystem().catch(error => {
  console.error('❌ 脚本执行失败:', error);
  process.exit(1);
});
