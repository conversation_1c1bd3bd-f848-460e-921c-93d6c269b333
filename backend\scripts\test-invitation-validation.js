/**
 * 测试邀请码验证功能
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001/api';
const INVITATION_CODE = 'NYaVGcw94Z7x4'; // 之前生成的邀请码

async function testInvitationValidation() {
  try {
    console.log('🧪 测试邀请码验证功能...\n');

    // 1. 测试验证邀请码API
    console.log(`📡 测试验证邀请码: ${INVITATION_CODE}`);
    const validateResponse = await fetch(`${BASE_URL}/admin/invitation-codes/validate/${INVITATION_CODE}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const validateData = await validateResponse.json();
    console.log('📊 验证响应:', JSON.stringify(validateData, null, 2));

    if (validateData.success && validateData.data.isValid) {
      console.log('✅ 邀请码验证成功');
    } else {
      console.log('❌ 邀请码验证失败');
      console.log(`💬 错误信息: ${validateData.data?.error || '未知错误'}`);
    }

    // 2. 测试注册时使用邀请码
    console.log('\n📡 测试注册时使用邀请码...');
    const registerData = {
      username: 'testuser_' + Date.now(),
      email: `test_${Date.now()}@example.com`,
      password: 'testpassword123',
      invitationCode: INVITATION_CODE
    };

    const registerResponse = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(registerData)
    });

    const registerResult = await registerResponse.json();
    console.log('📊 注册响应:', JSON.stringify(registerResult, null, 2));

    if (registerResult.success) {
      console.log('✅ 使用邀请码注册成功');
      console.log(`👤 新用户: ${registerResult.data.user.username}`);
      
      // 3. 再次验证邀请码（应该显示已被使用）
      console.log('\n📡 再次验证邀请码（应该显示已被使用）...');
      const validateAgainResponse = await fetch(`${BASE_URL}/admin/invitation-codes/validate/${INVITATION_CODE}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const validateAgainData = await validateAgainResponse.json();
      console.log('📊 再次验证响应:', JSON.stringify(validateAgainData, null, 2));

      if (!validateAgainData.data.isValid && validateAgainData.data.code === 'CODE_ALREADY_USED') {
        console.log('✅ 邀请码正确标记为已使用');
      } else {
        console.log('❌ 邀请码使用状态更新失败');
      }

    } else {
      console.log('❌ 使用邀请码注册失败');
      console.log(`💬 错误信息: ${registerResult.error?.message || '未知错误'}`);
    }

    console.log('\n🎉 邀请码验证测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testInvitationValidation();
