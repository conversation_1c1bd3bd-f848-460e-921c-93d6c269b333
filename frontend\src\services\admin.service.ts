import { API_BASE_URL } from '../config/api';
import { apiGet, apiPost, apiPut, apiDelete } from '../utils/api.utils';

export interface SystemStats {
  totalUsers: number;
  activeUsers: number;
  totalUploads: number;
  todayUploads: number;
  totalStorage: number;
  systemLoad: {
    cpu: number;
    memory: number;
    disk: number;
  };
  onlineUsers: number;
}

// 数据分析相关类型定义
export interface AnalyticsOverview {
  timeRange: 'day' | 'week' | 'month' | 'year';
  period: {
    start: string;
    end: string;
  };
  summary: {
    users: {
      total: number;
      new: number;
      active: number;
      banned: number;
    };
    uploads: {
      total: number;
      new: number;
      successful: number;
      failed: number;
    };
    access: {
      total: number;
      uniqueVisitors: number;
      pageViews: number;
    };
    storage: {
      totalSize: number;
      totalFiles: number;
      averageSize: number;
    };
  };
  trends: {
    uploads: Array<{ date: string; count: number }>;
    users: Array<{ date: string; count: number }>;
    access: Array<{ date: string; count: number }>;
  };
}

export interface UserAnalytics {
  timeRange: 'day' | 'week' | 'month' | 'year';
  period: { start: string; end: string };
  levelDistribution: Array<{
    level: string;
    count: number;
    percentage: number;
  }>;
  activityTrends: Array<{
    type: string;
    count: number;
  }>;
  geoDistribution: Array<{
    location: string;
    count: number;
  }>;
  deviceStats: {
    mobile: number;
    desktop: number;
    browsers: Record<string, number>;
  };
  activeUsers: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

export interface UploadAnalytics {
  timeRange: 'day' | 'week' | 'month' | 'year';
  period: { start: string; end: string };
  trends: Array<{ date: string; count: number }>;
  fileTypes: Array<{ type: string; count: number }>;
  fileSizes: Array<{ range: string; count: number }>;
  topUploaders: Array<{
    userId: number;
    username: string;
    userLevel: string;
    uploadCount: number;
  }>;
  successRate: {
    successful: number;
    failed: number;
    total: number;
    successRate: number;
  };
  popularImages: Array<{
    imageId: number;
    fileName: string;
    fileSize: number;
    mimeType: string;
    systemUrl: string;
    accessCount: number;
    uploader: {
      username: string;
      level: string;
    };
    totalAccess: number;
  }>;
}

export interface RealtimeStats {
  uploads: {
    today: number;
    yesterday: number;
    growth: number;
  };
  users: {
    online: number;
    newToday: number;
  };
  system: {
    cpu: number;
    memory: number;
    disk: number;
  };
}

// 安全管理相关类型定义
export interface IPSecurityOverview {
  summary: {
    totalIPs: number;
    blockedIPs: number;
    riskIPs: number;
    activeRules: number;
  };
  recentActivity: Array<{
    id: number;
    ipAddress: string;
    actionType: string;
    isBlocked: boolean;
    riskScore: number;
    createdAt: string;
    user?: {
      username: string;
    };
  }>;
  activeRules: Array<{
    id: number;
    ruleName: string;
    ruleType: string;
    isActive: boolean;
  }>;
}

export interface IPRiskLog {
  id: number;
  ipAddress: string;
  actionType: string;
  isBlocked: boolean;
  riskScore: number;
  blockExpiresAt?: string;
  userAgent?: string;
  createdAt: string;
  user?: {
    id: number;
    username: string;
    userLevel: string;
  };
  rule?: {
    id: number;
    ruleName: string;
    ruleType: string;
  };
}

export interface IPRiskRule {
  id: number;
  ruleName: string;
  ruleType: string;
  timeWindow: number;
  maxAttempts: number;
  blockDuration: number;
  isActive: boolean;
  createdAt: string;
  recentTriggers: number;
}

export interface UserPermissionsOverview {
  summary: {
    totalUsers: number;
    adminUsers: number;
    vipUsers: number;
    bannedUsers: number;
  };
  recentChanges: Array<{
    id: number;
    userId: number;
    oldLevel: string;
    newLevel: string;
    createdAt: string;
    user: {
      username: string;
    };
  }>;
}

export interface UserSession {
  id: number;
  userId: number;
  sessionToken: string;
  ipAddress: string;
  userAgent: string;
  expiresAt: string;
  createdAt: string;
  isActive: boolean;
  user: {
    id: number;
    username: string;
    userLevel: string;
    role: string;
  };
}

export interface SecurityConfig {
  id: number;
  configKey: string;
  configValue: any;
  updatedBy: number;
  updatedAt: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  userLevel: string;
  status: 'active' | 'suspended' | 'banned';
  totalUploads: number;
  storageUsed: number;
  createdAt: string;
  levelExpiresAt?: string;
}

export interface UsersResponse {
  users: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    timestamp: string;
  };
  timestamp: string;
}

class AdminService {
  private getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const fullEndpoint = `/admin${endpoint}`;

    try {
      switch (options.method?.toUpperCase()) {
        case 'POST':
          return await apiPost<T>(fullEndpoint, options.body ? JSON.parse(options.body as string) : undefined);
        case 'PUT':
          return await apiPut<T>(fullEndpoint, options.body ? JSON.parse(options.body as string) : undefined);
        case 'DELETE':
          return await apiDelete<T>(fullEndpoint);
        default:
          return await apiGet<T>(fullEndpoint);
      }
    } catch (error) {
      console.error(`AdminService请求失败 [${options.method || 'GET'}] ${fullEndpoint}:`, error);
      throw error;
    }
  }

  async getSystemStats(): Promise<SystemStats> {
    return this.request<SystemStats>('/stats');
  }

  async getUsers(params: {
    page?: number;
    limit?: number;
    search?: string;
    level?: string;
    status?: string;
  } = {}): Promise<UsersResponse> {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.level && params.level !== 'all') queryParams.append('level', params.level);
    if (params.status && params.status !== 'all') queryParams.append('status', params.status);

    const endpoint = `/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request<UsersResponse>(endpoint);
  }

  async updateUserLevel(userId: number, level: string, expiresAt?: string): Promise<User> {
    return this.request<User>(`/users/${userId}/level`, {
      method: 'PUT',
      body: JSON.stringify({ level, expiresAt }),
    });
  }

  async updateUserStatus(userId: number, status: string): Promise<User> {
    return this.request<User>(`/users/${userId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  }

  async getUserDetails(userId: number): Promise<User> {
    return this.request<User>(`/users/${userId}`);
  }

  async getRealtimeLogs(): Promise<any[]> {
    return this.request<any[]>('/logs/realtime');
  }

  // 系统监控相关方法
  async getSystemMonitoring(): Promise<any> {
    return this.request<any>('/monitoring/system');
  }

  async getMetricsHistory(
    type: string,
    startTime?: string,
    endTime?: string,
    limit?: number
  ): Promise<any> {
    const queryParams = new URLSearchParams();
    if (startTime) queryParams.append('startTime', startTime);
    if (endTime) queryParams.append('endTime', endTime);
    if (limit) queryParams.append('limit', limit.toString());

    const endpoint = `/monitoring/metrics/${type}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request<any>(endpoint);
  }

  async getSystemEvents(
    level?: string,
    startTime?: string,
    endTime?: string,
    limit?: number
  ): Promise<any> {
    const queryParams = new URLSearchParams();
    if (level) queryParams.append('level', level);
    if (startTime) queryParams.append('startTime', startTime);
    if (endTime) queryParams.append('endTime', endTime);
    if (limit) queryParams.append('limit', limit.toString());

    const endpoint = `/monitoring/events${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request<any>(endpoint);
  }

  async updateAlertThresholds(thresholds: {
    cpu?: number;
    memory?: number;
    disk?: number;
    errorRate?: number;
  }): Promise<any> {
    return this.request<any>('/monitoring/thresholds', {
      method: 'PUT',
      body: JSON.stringify(thresholds),
    });
  }

  async cleanupMonitoringData(daysToKeep: number = 30): Promise<any> {
    return this.request<any>('/monitoring/cleanup', {
      method: 'POST',
      body: JSON.stringify({ daysToKeep }),
    });
  }

  // 邀请码管理相关方法
  async getInvitationSystemConfig(): Promise<any> {
    return this.request<any>('/invitation-codes/config');
  }

  async updateInvitationSystemConfig(config: any): Promise<any> {
    return this.request<any>('/invitation-codes/config', {
      method: 'PUT',
      body: JSON.stringify(config),
    });
  }

  async enableInvitationSystem(): Promise<any> {
    return this.request<any>('/invitation-codes/enable', {
      method: 'POST',
    });
  }

  async disableInvitationSystem(): Promise<any> {
    return this.request<any>('/invitation-codes/disable', {
      method: 'POST',
    });
  }

  async getInvitationCodes(options: {
    page?: number;
    limit?: number;
    isUsed?: boolean;
    search?: string;
    sortBy?: string;
    sortOrder?: string;
  } = {}): Promise<any> {
    const queryParams = new URLSearchParams();
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/invitation-codes${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request<any>(endpoint);
  }

  async generateInvitationCode(data: {
    description?: string;
    expiresAt?: string;
  }): Promise<any> {
    return this.request<any>('/invitation-codes/generate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async generateBatchInvitationCodes(data: {
    count: number;
    description?: string;
    expiresAt?: string;
  }): Promise<any> {
    return this.request<any>('/invitation-codes/generate-batch', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async deleteInvitationCode(id: number): Promise<any> {
    return this.request<any>(`/invitation-codes/${id}`, {
      method: 'DELETE',
    });
  }

  async deleteBatchInvitationCodes(ids: number[]): Promise<any> {
    return this.request<any>('/invitation-codes/delete-batch', {
      method: 'POST',
      body: JSON.stringify({ ids }),
    });
  }

  async getInvitationCodeStatistics(): Promise<any> {
    return this.request<any>('/invitation-codes/statistics');
  }

  async cleanupExpiredInvitationCodes(): Promise<any> {
    return this.request<any>('/invitation-codes/cleanup-expired', {
      method: 'POST',
    });
  }

  // 数据分析相关方法
  async getAnalyticsOverview(timeRange: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<AnalyticsOverview> {
    const queryParams = new URLSearchParams();
    queryParams.append('timeRange', timeRange);

    const endpoint = `/analytics/overview?${queryParams.toString()}`;
    return this.request<AnalyticsOverview>(endpoint);
  }

  async getUserAnalytics(timeRange: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<UserAnalytics> {
    const queryParams = new URLSearchParams();
    queryParams.append('timeRange', timeRange);

    const endpoint = `/analytics/users?${queryParams.toString()}`;
    return this.request<UserAnalytics>(endpoint);
  }

  async getUploadAnalytics(timeRange: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<UploadAnalytics> {
    const queryParams = new URLSearchParams();
    queryParams.append('timeRange', timeRange);

    const endpoint = `/analytics/uploads?${queryParams.toString()}`;
    return this.request<UploadAnalytics>(endpoint);
  }

  async getRealtimeAnalytics(): Promise<RealtimeStats> {
    return this.request<RealtimeStats>('/analytics/realtime');
  }

  // 日志管理相关方法
  async clearRealtimeLogs(logType?: string): Promise<any> {
    return this.request<any>('/logs/clear-realtime', {
      method: 'POST',
      body: JSON.stringify({ logType: logType || 'all' }),
    });
  }

  // 安全管理相关方法
  async getIPSecurityOverview(): Promise<IPSecurityOverview> {
    return this.request<IPSecurityOverview>('/security/ip-overview');
  }

  async getIPRiskLogs(params?: {
    page?: number;
    limit?: number;
    ipAddress?: string;
    actionType?: string;
    isBlocked?: boolean;
    startDate?: string;
    endDate?: string;
  }): Promise<{ logs: IPRiskLog[]; pagination: any }> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/security/ip-logs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request<{ logs: IPRiskLog[]; pagination: any }>(endpoint);
  }

  async getIPRiskRules(): Promise<IPRiskRule[]> {
    return this.request<IPRiskRule[]>('/security/ip-rules');
  }

  async createIPRiskRule(data: {
    ruleName: string;
    ruleType: string;
    timeWindow: number;
    maxAttempts: number;
    blockDuration: number;
    isActive?: boolean;
  }): Promise<IPRiskRule> {
    return this.request<IPRiskRule>('/security/ip-rules', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateIPRiskRule(id: number, data: Partial<IPRiskRule>): Promise<IPRiskRule> {
    return this.request<IPRiskRule>(`/security/ip-rules/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteIPRiskRule(id: number): Promise<any> {
    return this.request<any>(`/security/ip-rules/${id}`, {
      method: 'DELETE',
    });
  }

  async blockIP(data: {
    ipAddress: string;
    blockDuration: number;
    reason: string;
  }): Promise<any> {
    return this.request<any>('/security/block-ip', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async unblockIP(ipAddress: string): Promise<any> {
    return this.request<any>('/security/unblock-ip', {
      method: 'POST',
      body: JSON.stringify({ ipAddress }),
    });
  }

  async getUserPermissionsOverview(): Promise<UserPermissionsOverview> {
    return this.request<UserPermissionsOverview>('/security/permissions-overview');
  }

  async getUserSessions(params?: {
    page?: number;
    limit?: number;
    userId?: number;
    ipAddress?: string;
    isActive?: boolean;
  }): Promise<{ sessions: UserSession[]; pagination: any }> {
    const queryParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/security/sessions${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.request<{ sessions: UserSession[]; pagination: any }>(endpoint);
  }

  async revokeUserSession(sessionId: number): Promise<any> {
    return this.request<any>(`/security/sessions/${sessionId}`, {
      method: 'DELETE',
    });
  }

  async getSecurityConfigs(): Promise<SecurityConfig[]> {
    return this.request<SecurityConfig[]>('/security/configs');
  }

  async updateSecurityConfig(configKey: string, configValue: any): Promise<SecurityConfig> {
    return this.request<SecurityConfig>('/security/configs', {
      method: 'PUT',
      body: JSON.stringify({ configKey, configValue }),
    });
  }
}

export const adminService = new AdminService();