import { Request, Response } from 'express';
import { prisma } from '../config/database';
import { ApiResponse, ErrorCodes } from '../types';
import { LogService } from '../services/log.service';
import { SystemMonitorService } from '../services/system-monitor.service';
import { AnalyticsService } from '../services/analytics.service';
import { SecurityService } from '../services/security.service';

export class AdminController {
  /**
   * 获取系统统计数据
   */
  static async getSystemStats(req: Request, res: Response): Promise<void> {
    try {
      // 获取用户统计
      const totalUsers = await prisma.user.count();
      const activeUsers = await prisma.user.count({
        where: {
          status: 'active'
        }
      });

      // 获取上传统计
      const totalUploads = await prisma.userImage.count();
      const todayUploads = await prisma.userImage.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      });

      // 获取存储统计
      const storageStats = await prisma.image.aggregate({
        _sum: {
          fileSize: true
        },
        where: {
          isDeleted: false
        }
      });

      // 模拟系统负载数据（实际项目中应该从系统监控服务获取）
      const systemLoad = {
        cpu: Math.floor(Math.random() * 100),
        memory: Math.floor(Math.random() * 100),
        disk: Math.floor(Math.random() * 100)
      };

      // 获取在线用户数（模拟数据）
      const onlineUsers = Math.floor(Math.random() * 50);

      const stats = {
        totalUsers,
        activeUsers,
        totalUploads,
        todayUploads,
        totalStorage: storageStats._sum.fileSize || 0,
        systemLoad,
        onlineUsers
      };

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取系统统计失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取系统统计失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }



  /**
   * 更新用户等级
   */
  static async updateUserLevel(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { level, expiresAt } = req.body;

      // 验证等级
      const validLevels = ['free', 'vip1', 'vip2', 'vip3'];
      if (!validLevels.includes(level)) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.INVALID_INPUT,
            message: '无效的用户等级',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: Number(id) }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 不能修改管理员的等级
      if (user.role === 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: ErrorCodes.FORBIDDEN,
            message: '不能修改管理员的等级',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 更新用户等级
      const updatedUser = await prisma.user.update({
        where: { id: Number(id) },
        data: {
          userLevel: level,
          levelExpiresAt: expiresAt ? new Date(expiresAt) : null
        },
        select: {
          id: true,
          username: true,
          email: true,
          userLevel: true,
          levelExpiresAt: true
        }
      });

      res.json({
        success: true,
        data: updatedUser,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('更新用户等级失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '更新用户等级失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }



  /**
   * 获取用户详情
   */
  static async getUserDetails(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const user = await prisma.user.findUnique({
        where: { id: Number(id) },
        include: {
          userImages: {
            include: {
              image: true
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 10 // 最近10次上传
          }
        }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      res.json({
        success: true,
        data: user,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户详情失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户详情失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 获取实时日志（用于初始化连接时获取最近的日志）
  static async getRealtimeLogs(req: Request, res: Response): Promise<void> {
    try {
      const { type = 'all', limit = 100 } = req.query;

      let logs: any[] = [];

      if (type === 'all' || type === 'system') {
        const systemLogs = await LogService.getSystemLogs({
          page: 1,
          limit: parseInt(limit as string) || 100,
        });
        logs = [...logs, ...systemLogs.logs.map(log => ({ ...log, type: 'system' }))];
      }

      if (type === 'all' || type === 'upload') {
        const uploadLogs = await LogService.getUploadLogs({
          page: 1,
          limit: parseInt(limit as string) || 100,
        });
        logs = [...logs, ...uploadLogs.logs.map(log => ({ ...log, type: 'upload' }))];
      }

      // 按时间排序
      logs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      res.json({
        success: true,
        data: logs.slice(0, parseInt(limit as string) || 100),
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取实时日志失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取实时日志失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  static async getSystemLogs(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 50,
        levels,
        types,
        startDate,
        endDate
      } = req.query;

      const filters: any = {};

      if (levels) {
        filters.levels = (levels as string).split(',');
      }

      if (types) {
        filters.types = (types as string).split(',');
      }

      if (startDate) {
        filters.startDate = startDate as string;
      }

      if (endDate) {
        filters.endDate = endDate as string;
      }

      const result = await LogService.getSystemLogs({
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        filters,
      });

      res.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取系统日志失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取系统日志失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 用户管理相关方法

  // 获取用户列表
  static async getUsers(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        status,
        role,
        userLevel,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
      const take = parseInt(limit as string);

      // 构建查询条件
      const where: any = {};

      if (search) {
        where.OR = [
          { username: { contains: search as string, mode: 'insensitive' } },
          { email: { contains: search as string, mode: 'insensitive' } },
          { displayName: { contains: search as string, mode: 'insensitive' } }
        ];
      }

      if (status) {
        where.status = status;
      }

      if (role) {
        where.role = role;
      }

      if (userLevel) {
        where.userLevel = userLevel;
      }

      // 构建排序条件
      const orderBy: any = {};
      orderBy[sortBy as string] = sortOrder;

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          orderBy,
          skip,
          take,
          select: {
            id: true,
            username: true,
            email: true,
            displayName: true,
            role: true,
            userLevel: true,
            status: true,
            avatarUrl: true,
            createdAt: true,
            updatedAt: true,
            lastLoginAt: true,
            lastActiveAt: true,
            levelExpiresAt: true,
            _count: {
              select: {
                userImages: true,
                uploadLogs: true
              }
            }
          }
        }),
        prisma.user.count({ where })
      ]);

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            totalPages: Math.ceil(total / parseInt(limit as string))
          }
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户列表失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户列表失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 获取用户详细信息
  static async getUserDetail(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const adminId = parseInt((req as any).user.id);

      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '用户ID是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        include: {
          userSettings: true,
          _count: {
            select: {
              userImages: true,
              uploadLogs: true,
              activityLogs: true,
              loginHistory: true
            }
          }
        }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.USER_NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminId,
        'view_user_detail',
        'user',
        user.id,
        { viewedUser: user.username },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        data: user,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户详情失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户详情失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 更新用户状态
  static async updateUserStatus(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { status, reason } = req.body;
      const adminId = parseInt((req as any).user.id);

      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '用户ID是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const validStatuses = ['active', 'suspended', 'banned'];
      if (!validStatuses.includes(status)) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '无效的用户状态',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        select: { id: true, username: true, status: true, role: true }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.USER_NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 不能修改其他管理员的状态
      if (user.role === 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: ErrorCodes.PERMISSION_DENIED,
            message: '不能修改管理员的状态',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const updatedUser = await prisma.user.update({
        where: { id: parseInt(userId) },
        data: {
          status,
          updatedAt: new Date()
        },
        select: {
          id: true,
          username: true,
          status: true,
          updatedAt: true
        }
      });

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminId,
        'update_user_status',
        'user',
        user.id,
        {
          oldStatus: user.status,
          newStatus: status,
          reason,
          targetUser: user.username
        },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        data: updatedUser,
        message: '用户状态更新成功',
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('更新用户状态失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '更新用户状态失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 更新用户基本信息
  static async updateUserProfile(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { displayName, bio, location, website, profileVisibility, allowDirectMessages, showOnlineStatus } = req.body;
      const adminId = parseInt((req as any).user.id);

      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '用户ID是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        select: { id: true, username: true, role: true }
      });

      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.USER_NOT_FOUND,
            message: '用户不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 不能修改其他管理员的信息
      if (user.role === 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: ErrorCodes.PERMISSION_DENIED,
            message: '不能修改管理员的信息',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const updatedUser = await prisma.user.update({
        where: { id: parseInt(userId) },
        data: {
          displayName: displayName || null,
          bio: bio || null,
          location: location || null,
          website: website || null,
          profileVisibility: profileVisibility || undefined,
          allowDirectMessages: allowDirectMessages !== undefined ? allowDirectMessages : undefined,
          showOnlineStatus: showOnlineStatus !== undefined ? showOnlineStatus : undefined,
          updatedAt: new Date()
        },
        select: {
          id: true,
          username: true,
          displayName: true,
          bio: true,
          location: true,
          website: true,
          profileVisibility: true,
          allowDirectMessages: true,
          showOnlineStatus: true,
          updatedAt: true
        }
      });

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminId,
        'update_user_profile',
        'user',
        user.id,
        {
          changes: req.body,
          targetUser: user.username
        },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        data: updatedUser,
        message: '用户信息更新成功',
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('更新用户信息失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '更新用户信息失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 获取用户活动日志
  static async getUserActivityLogs(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 50 } = req.query;
      const adminId = parseInt((req as any).user.id);

      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '用户ID是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
      const take = parseInt(limit as string);

      const [logs, total] = await Promise.all([
        prisma.userActivityLog.findMany({
          where: { userId: parseInt(userId) },
          orderBy: { createdAt: 'desc' },
          skip,
          take,
          select: {
            id: true,
            activityType: true,
            activityData: true,
            ipAddress: true,
            location: true,
            deviceInfo: true,
            createdAt: true
          }
        }),
        prisma.userActivityLog.count({ where: { userId: parseInt(userId) } })
      ]);

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminId,
        'view_user_activity_logs',
        'user',
        parseInt(userId),
        { viewedLogsCount: logs.length },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        data: {
          logs,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            totalPages: Math.ceil(total / parseInt(limit as string))
          }
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户活动日志失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户活动日志失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 获取用户登录历史
  static async getUserLoginHistory(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 50 } = req.query;
      const adminId = parseInt((req as any).user.id);

      if (!userId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '用户ID是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
      const take = parseInt(limit as string);

      const [history, total] = await Promise.all([
        prisma.userLoginHistory.findMany({
          where: { userId: parseInt(userId) },
          orderBy: { createdAt: 'desc' },
          skip,
          take,
          select: {
            id: true,
            loginType: true,
            isSuccess: true,
            failureReason: true,
            ipAddress: true,
            location: true,
            deviceInfo: true,
            createdAt: true
          }
        }),
        prisma.userLoginHistory.count({ where: { userId: parseInt(userId) } })
      ]);

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminId,
        'view_user_login_history',
        'user',
        parseInt(userId),
        { viewedHistoryCount: history.length },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        data: {
          history,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            totalPages: Math.ceil(total / parseInt(limit as string))
          }
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取用户登录历史失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户登录历史失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 获取等级申请列表
  static async getLevelApplications(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        status = 'all',
        requestedLevel,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
      const take = parseInt(limit as string);

      // 构建查询条件
      const where: any = {};

      if (status !== 'all') {
        where.status = status;
      }

      if (requestedLevel && requestedLevel !== 'all') {
        where.requestedLevel = requestedLevel;
      }

      // 构建排序条件
      const orderBy: any = {};
      orderBy[sortBy as string] = sortOrder;

      const [applications, total] = await Promise.all([
        prisma.userLevelApplication.findMany({
          where,
          orderBy,
          skip,
          take,
          include: {
            user: {
              select: {
                id: true,
                username: true,
                email: true,
                displayName: true,
                userLevel: true,
                createdAt: true
              }
            },
            admin: {
              select: {
                username: true,
                displayName: true
              }
            }
          }
        }),
        prisma.userLevelApplication.count({ where })
      ]);

      res.json({
        success: true,
        data: {
          applications,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            totalPages: Math.ceil(total / parseInt(limit as string))
          }
        },
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('获取等级申请列表失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取等级申请列表失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 处理等级申请（批准或拒绝）
  static async processLevelApplication(req: Request, res: Response): Promise<void> {
    try {
      const { applicationId } = req.params;
      const { action, comment } = req.body; // action: 'approve' | 'reject'
      const adminId = parseInt((req as any).user.id);

      if (!applicationId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '申请ID是必需的',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      if (!['approve', 'reject'].includes(action)) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '无效的操作类型',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 查找申请记录
      const application = await prisma.userLevelApplication.findUnique({
        where: { id: parseInt(applicationId) },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              userLevel: true,
              email: true
            }
          }
        }
      });

      if (!application) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '申请记录不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      if (application.status !== 'pending') {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '该申请已被处理',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const newStatus = action === 'approve' ? 'approved' : 'rejected';
      const processedAt = new Date();

      // 使用事务处理
      const result = await prisma.$transaction(async (tx) => {
        // 更新申请状态
        const updatedApplication = await tx.userLevelApplication.update({
          where: { id: parseInt(applicationId) },
          data: {
            status: newStatus,
            adminId,
            adminComment: comment || null,
            processedAt,
            updatedAt: new Date()
          }
        });

        // 如果批准，更新用户等级
        if (action === 'approve') {
          await tx.user.update({
            where: { id: application.userId },
            data: {
              userLevel: application.requestedLevel,
              levelExpiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1年后过期
              updatedAt: new Date()
            }
          });

          // 记录等级变更历史
          await tx.userLevelHistory.create({
            data: {
              userId: application.userId,
              oldLevel: application.currentLevel,
              newLevel: application.requestedLevel,
              changedBy: adminId,
              reason: `等级申请批准: ${comment || '无备注'}`
            }
          });
        }

        return updatedApplication;
      });

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminId,
        `level_application_${action}`,
        'level_application',
        parseInt(applicationId),
        {
          targetUser: application.user.username,
          currentLevel: application.currentLevel,
          requestedLevel: application.requestedLevel,
          comment: comment || null
        },
        req.ip,
        req.get('User-Agent')
      );

      // 记录系统事件
      await LogService.logSystemEvent(
        `level_application_${action}`,
        'info',
        `管理员${action === 'approve' ? '批准' : '拒绝'}了用户 ${application.user.username} 的等级申请`,
        {
          applicationId: parseInt(applicationId),
          userId: application.userId,
          adminId,
          currentLevel: application.currentLevel,
          requestedLevel: application.requestedLevel,
          comment: comment || null
        }
      );

      res.json({
        success: true,
        data: result,
        message: `申请已${action === 'approve' ? '批准' : '拒绝'}`,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('处理等级申请失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '处理等级申请失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 删除已完成的等级申请记录
  static async deleteLevelApplication(req: Request, res: Response): Promise<void> {
    try {
      const { applicationId } = req.params;
      const adminId = parseInt((req as any).user.id);

      // 验证申请ID
      if (!applicationId || isNaN(parseInt(applicationId))) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '无效的申请ID',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 查找申请记录
      const application = await prisma.userLevelApplication.findUnique({
        where: { id: parseInt(applicationId) },
        include: {
          user: {
            select: { username: true, email: true }
          }
        }
      });

      if (!application) {
        res.status(404).json({
          success: false,
          error: {
            code: ErrorCodes.NOT_FOUND,
            message: '申请记录不存在',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 只允许删除已完成的申请（已批准、已拒绝、已取消）
      if (application.status === 'pending') {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '不能删除待处理的申请，请先处理申请',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      // 删除申请记录
      await prisma.userLevelApplication.delete({
        where: { id: parseInt(applicationId) }
      });

      // 记录管理员操作
      await LogService.logAdminOperation(
        adminId,
        'delete_level_application',
        'level_application',
        parseInt(applicationId),
        {
          applicationId: parseInt(applicationId),
          userId: application.userId,
          username: application.user.username,
          currentLevel: application.currentLevel,
          requestedLevel: application.requestedLevel,
          status: application.status,
          reason: application.reason.substring(0, 100)
        },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        message: '申请记录已删除',
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('删除等级申请失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '删除等级申请失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  static async getProviders(req: Request, res: Response): Promise<void> {
    // TODO: 实现接口提供商列表获取
    res.json({ success: true, data: [], timestamp: new Date().toISOString() });
  }

  static async createProvider(req: Request, res: Response): Promise<void> {
    // TODO: 实现创建接口提供商
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async updateProvider(req: Request, res: Response): Promise<void> {
    // TODO: 实现更新接口提供商
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async deleteProvider(req: Request, res: Response): Promise<void> {
    // TODO: 实现删除接口提供商
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async getSystemMonitoring(req: Request, res: Response): Promise<void> {
    try {
      const monitorService = new SystemMonitorService();

      // 获取当前系统状态摘要
      const systemSummary = await monitorService.getSystemSummary();

      // 获取最近的系统事件（最近24小时）
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const recentEvents = await monitorService.getSystemEvents(
        undefined, // 所有级别
        yesterday,
        new Date(),
        20 // 最近20条
      );

      // 获取历史CPU使用率数据（最近1小时）
      const oneHourAgo = new Date();
      oneHourAgo.setHours(oneHourAgo.getHours() - 1);
      const cpuHistory = await monitorService.getHistoricalMetrics(
        'cpu_usage',
        oneHourAgo,
        new Date(),
        60 // 最近60个数据点
      );

      // 获取历史内存使用率数据（最近1小时）
      const memoryHistory = await monitorService.getHistoricalMetrics(
        'memory_usage',
        oneHourAgo,
        new Date(),
        60
      );

      const response: ApiResponse<any> = {
        success: true,
        data: {
          summary: systemSummary,
          events: recentEvents,
          charts: {
            cpu: cpuHistory.map(item => ({
              timestamp: item.createdAt,
              value: Number(item.metricValue),
              unit: item.unit
            })),
            memory: memoryHistory.map(item => ({
              timestamp: item.createdAt,
              value: Number(item.metricValue),
              unit: item.unit
            }))
          },
          thresholds: monitorService.getAlertThresholds()
        }
      };

      res.json(response);
    } catch (error) {
      console.error('获取系统监控数据失败:', error);

      const errorResponse: ApiResponse<null> = {
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取系统监控数据失败',
          details: (error as Error).message,
          timestamp: new Date().toISOString()
        }
      };

      res.status(500).json(errorResponse);
    }
  }

  /**
   * 获取特定类型的历史指标数据
   */
  static async getMetricsHistory(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.params;
      const { startTime, endTime, limit = '100' } = req.query;

      if (!type) {
        const errorResponse: ApiResponse<null> = {
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '缺少指标类型参数',
            timestamp: new Date().toISOString()
          }
        };
        res.status(400).json(errorResponse);
        return;
      }

      const monitorService = new SystemMonitorService();

      // 设置默认时间范围（最近24小时）
      const defaultEndTime = new Date();
      const defaultStartTime = new Date();
      defaultStartTime.setDate(defaultStartTime.getDate() - 1);

      const start = startTime ? new Date(startTime as string) : defaultStartTime;
      const end = endTime ? new Date(endTime as string) : defaultEndTime;
      const limitNum = parseInt(limit as string, 10);

      const metrics = await monitorService.getHistoricalMetrics(
        type,
        start,
        end,
        limitNum
      );

      const response: ApiResponse<any> = {
        success: true,
        data: {
          metricType: type,
          timeRange: { start, end },
          data: metrics.map(item => ({
            timestamp: item.createdAt,
            value: Number(item.metricValue),
            unit: item.unit,
            serverInstance: item.serverInstance
          }))
        }
      };

      res.json(response);
    } catch (error) {
      console.error('获取指标历史数据失败:', error);

      const errorResponse: ApiResponse<null> = {
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取指标历史数据失败',
          details: (error as Error).message,
          timestamp: new Date().toISOString()
        }
      };

      res.status(500).json(errorResponse);
    }
  }

  /**
   * 获取系统事件日志
   */
  static async getSystemEvents(req: Request, res: Response): Promise<void> {
    try {
      const { level, startTime, endTime, limit = '50' } = req.query;

      const monitorService = new SystemMonitorService();

      // 设置默认时间范围（最近24小时）
      const defaultEndTime = new Date();
      const defaultStartTime = new Date();
      defaultStartTime.setDate(defaultStartTime.getDate() - 1);

      const start = startTime ? new Date(startTime as string) : defaultStartTime;
      const end = endTime ? new Date(endTime as string) : defaultEndTime;
      const limitNum = parseInt(limit as string, 10);

      const events = await monitorService.getSystemEvents(
        level as string,
        start,
        end,
        limitNum
      );

      const response: ApiResponse<any> = {
        success: true,
        data: {
          events,
          filters: {
            level: level || 'all',
            timeRange: { start, end }
          }
        }
      };

      res.json(response);
    } catch (error) {
      console.error('获取系统事件日志失败:', error);

      const errorResponse: ApiResponse<null> = {
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取系统事件日志失败',
          details: (error as Error).message,
          timestamp: new Date().toISOString()
        }
      };

      res.status(500).json(errorResponse);
    }
  }

  /**
   * 更新告警阈值配置
   */
  static async updateAlertThresholds(req: Request, res: Response): Promise<void> {
    try {
      const { cpu, memory, disk, errorRate } = req.body;
      const adminUser = (req as any).user;

      // 验证阈值参数
      const thresholds: any = {};
      if (cpu !== undefined) thresholds.cpu = Math.max(0, Math.min(100, cpu));
      if (memory !== undefined) thresholds.memory = Math.max(0, Math.min(100, memory));
      if (disk !== undefined) thresholds.disk = Math.max(0, Math.min(100, disk));
      if (errorRate !== undefined) thresholds.errorRate = Math.max(0, Math.min(100, errorRate));

      // 这里应该将阈值保存到数据库或配置文件中
      // 目前先记录管理员操作日志
      await LogService.logAdminOperation(
        adminUser.id,
        'update_alert_thresholds',
        'system',
        undefined,
        { oldThresholds: {}, newThresholds: thresholds },
        req.ip,
        req.get('User-Agent')
      );

      const response: ApiResponse<any> = {
        success: true,
        data: {
          message: '告警阈值已更新',
          thresholds
        }
      };

      res.json(response);
    } catch (error) {
      console.error('更新告警阈值失败:', error);

      const errorResponse: ApiResponse<null> = {
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '更新告警阈值失败',
          details: (error as Error).message,
          timestamp: new Date().toISOString()
        }
      };

      res.status(500).json(errorResponse);
    }
  }

  /**
   * 清理过期监控数据
   */
  static async cleanupMonitoringData(req: Request, res: Response): Promise<void> {
    try {
      const { daysToKeep = 30 } = req.body;
      const adminUser = (req as any).user;

      const monitorService = new SystemMonitorService();
      await monitorService.cleanupOldData(daysToKeep);

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminUser.id,
        'cleanup_monitoring_data',
        'system',
        undefined,
        { daysToKeep },
        req.ip,
        req.get('User-Agent')
      );

      const response: ApiResponse<any> = {
        success: true,
        data: {
          message: `已清理 ${daysToKeep} 天前的监控数据`
        }
      };

      res.json(response);
    } catch (error) {
      console.error('清理监控数据失败:', error);

      const errorResponse: ApiResponse<null> = {
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '清理监控数据失败',
          details: (error as Error).message,
          timestamp: new Date().toISOString()
        }
      };

      res.status(500).json(errorResponse);
    }
  }

  /**
   * 获取数据保留策略
   */
  static async getDataRetentionConfig(req: Request, res: Response): Promise<void> {
    try {
      const monitorService = new SystemMonitorService();
      const config = monitorService.getDataRetentionConfig();

      const response: ApiResponse<any> = {
        success: true,
        data: config
      };

      res.json(response);
    } catch (error) {
      console.error('获取数据保留策略失败:', error);

      const errorResponse: ApiResponse<null> = {
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取数据保留策略失败',
          details: (error as Error).message,
          timestamp: new Date().toISOString()
        }
      };

      res.status(500).json(errorResponse);
    }
  }

  /**
   * 更新数据保留策略
   */
  static async updateDataRetentionConfig(req: Request, res: Response): Promise<void> {
    try {
      const { metricsRetentionHours, eventsRetentionDays, cleanupIntervalMinutes } = req.body;
      const adminUser = (req as any).user;

      const config: any = {};
      if (metricsRetentionHours !== undefined) config.metricsRetentionHours = Math.max(1, metricsRetentionHours);
      if (eventsRetentionDays !== undefined) config.eventsRetentionDays = Math.max(1, eventsRetentionDays);
      if (cleanupIntervalMinutes !== undefined) config.cleanupIntervalMinutes = Math.max(5, cleanupIntervalMinutes);

      const monitorService = new SystemMonitorService();
      monitorService.setDataRetentionConfig(config);

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminUser.id,
        'update_data_retention_config',
        'system',
        undefined,
        { newConfig: config },
        req.ip,
        req.get('User-Agent')
      );

      const response: ApiResponse<any> = {
        success: true,
        data: {
          message: '数据保留策略已更新',
          config: monitorService.getDataRetentionConfig()
        }
      };

      res.json(response);
    } catch (error) {
      console.error('更新数据保留策略失败:', error);

      const errorResponse: ApiResponse<null> = {
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '更新数据保留策略失败',
          details: (error as Error).message,
          timestamp: new Date().toISOString()
        }
      };

      res.status(500).json(errorResponse);
    }
  }

  /**
   * 手动触发数据清理
   */
  static async triggerDataCleanup(req: Request, res: Response): Promise<void> {
    try {
      const adminUser = (req as any).user;
      const monitorService = new SystemMonitorService();

      // 执行数据清理
      await (monitorService as any).performDataCleanup();

      // 记录管理员操作日志
      await LogService.logAdminOperation(
        adminUser.id,
        'trigger_data_cleanup',
        'system',
        undefined,
        { triggeredAt: new Date().toISOString() },
        req.ip,
        req.get('User-Agent')
      );

      const response: ApiResponse<any> = {
        success: true,
        data: {
          message: '数据清理任务已执行'
        }
      };

      res.json(response);
    } catch (error) {
      console.error('手动数据清理失败:', error);

      const errorResponse: ApiResponse<null> = {
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '手动数据清理失败',
          details: (error as Error).message,
          timestamp: new Date().toISOString()
        }
      };

      res.status(500).json(errorResponse);
    }
  }

  static async getIPSecurity(req: Request, res: Response): Promise<void> {
    // TODO: 实现IP安全数据获取
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async addIPToBlacklist(req: Request, res: Response): Promise<void> {
    // TODO: 实现添加IP到黑名单
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async removeIPFromBlacklist(req: Request, res: Response): Promise<void> {
    // TODO: 实现从黑名单移除IP
    res.json({ success: true, data: {}, timestamp: new Date().toISOString() });
  }

  static async getAnalyticsOverview(req: Request, res: Response): Promise<void> {
    try {
      const { timeRange = 'month' } = req.query;
      const analyticsService = new AnalyticsService();

      const overview = await analyticsService.getAnalyticsOverview(
        timeRange as 'day' | 'week' | 'month' | 'year'
      );

      const response: ApiResponse<any> = {
        success: true,
        data: overview
      };

      res.json(response);
    } catch (error) {
      console.error('获取数据分析概览失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取数据分析概览失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  static async getUserAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const { timeRange = 'month' } = req.query;
      const analyticsService = new AnalyticsService();

      const userAnalytics = await analyticsService.getUserAnalytics(
        timeRange as 'day' | 'week' | 'month' | 'year'
      );

      const response: ApiResponse<any> = {
        success: true,
        data: userAnalytics
      };

      res.json(response);
    } catch (error) {
      console.error('获取用户行为分析失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户行为分析失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  static async getUploadAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const { timeRange = 'month' } = req.query;
      const analyticsService = new AnalyticsService();

      const uploadAnalytics = await analyticsService.getUploadAnalytics(
        timeRange as 'day' | 'week' | 'month' | 'year'
      );

      const response: ApiResponse<any> = {
        success: true,
        data: uploadAnalytics
      };

      res.json(response);
    } catch (error) {
      console.error('获取上传数据分析失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取上传数据分析失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 获取实时统计数据
   */
  static async getRealtimeAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const analyticsService = new AnalyticsService();
      const realtimeStats = await analyticsService.getRealtimeStats();

      const response: ApiResponse<any> = {
        success: true,
        data: realtimeStats
      };

      res.json(response);
    } catch (error) {
      console.error('获取实时统计数据失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取实时统计数据失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 清除实时系统日志
   */
  static async clearRealtimeLogs(req: Request, res: Response): Promise<void> {
    try {
      const { logType } = req.body;

      let deleteCondition: any = {};

      // 如果指定了日志类型，只删除特定类型的日志
      if (logType && logType !== 'all') {
        deleteCondition.eventType = logType;
      }

      // 删除系统事件日志
      const deletedEventLogs = await prisma.systemEventLog.deleteMany({
        where: deleteCondition
      });

      // 记录清除操作
      await LogService.logAdminOperation(
        Number(req.user?.id) || 0,
        'clear_realtime_logs',
        'system_logs',
        undefined,
        {
          logType: logType || 'all',
          deletedCount: deletedEventLogs.count,
          timestamp: new Date().toISOString()
        },
        req.ip || 'unknown'
      );

      const response: ApiResponse<any> = {
        success: true,
        data: {
          deletedCount: deletedEventLogs.count,
          logType: logType || 'all',
          message: `成功清除 ${deletedEventLogs.count} 条日志记录`
        }
      };

      res.json(response);
    } catch (error) {
      console.error('清除实时日志失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '清除实时日志失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // 清理系统日志
  static async clearSystemLogs(req: Request, res: Response): Promise<void> {
    try {
      const adminId = parseInt((req as any).user.id);
      const { logTypes, olderThan } = req.body;

      // 构建删除条件
      const deleteConditions: any[] = [];

      // 如果指定了日志类型，只删除特定类型的日志
      if (logTypes && Array.isArray(logTypes) && logTypes.length > 0) {
        // 删除系统事件日志
        if (logTypes.includes('system')) {
          const systemDeleteCondition: any = {};
          if (olderThan) {
            systemDeleteCondition.createdAt = { lt: new Date(olderThan) };
          }
          deleteConditions.push(
            prisma.systemEventLog.deleteMany({ where: systemDeleteCondition })
          );
        }

        // 删除上传日志
        if (logTypes.includes('upload')) {
          const uploadDeleteCondition: any = {};
          if (olderThan) {
            uploadDeleteCondition.createdAt = { lt: new Date(olderThan) };
          }
          deleteConditions.push(
            prisma.uploadLog.deleteMany({ where: uploadDeleteCondition })
          );
        }

        // 删除访问日志
        if (logTypes.includes('access')) {
          const accessDeleteCondition: any = {};
          if (olderThan) {
            accessDeleteCondition.createdAt = { lt: new Date(olderThan) };
          }
          deleteConditions.push(
            prisma.accessLog.deleteMany({ where: accessDeleteCondition })
          );
        }

        // 删除管理员操作日志
        if (logTypes.includes('admin')) {
          const adminDeleteCondition: any = {};
          if (olderThan) {
            adminDeleteCondition.createdAt = { lt: new Date(olderThan) };
          }
          deleteConditions.push(
            prisma.adminOperationLog.deleteMany({ where: adminDeleteCondition })
          );
        }
      } else {
        // 如果没有指定类型，删除所有类型的日志
        const deleteCondition: any = {};
        if (olderThan) {
          deleteCondition.createdAt = { lt: new Date(olderThan) };
        }

        deleteConditions.push(
          prisma.systemEventLog.deleteMany({ where: deleteCondition }),
          prisma.uploadLog.deleteMany({ where: deleteCondition }),
          prisma.accessLog.deleteMany({ where: deleteCondition }),
          prisma.adminOperationLog.deleteMany({ where: deleteCondition })
        );
      }

      // 执行删除操作
      const results = await Promise.all(deleteConditions);
      const totalDeleted = results.reduce((sum, result) => sum + result.count, 0);

      // 记录管理员操作
      await LogService.logAdminOperation(
        adminId,
        'clear_system_logs',
        'system',
        undefined,
        {
          logTypes: logTypes || ['all'],
          olderThan,
          deletedCount: totalDeleted
        },
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        data: {
          deletedCount: totalDeleted,
          logTypes: logTypes || ['all'],
          olderThan
        },
        message: `成功清理了 ${totalDeleted} 条日志记录`,
        timestamp: new Date().toISOString()
      } as ApiResponse);

    } catch (error) {
      console.error('清理系统日志失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '清理系统日志失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  // ==================== 安全管理相关方法 ====================

  /**
   * 获取IP风控概览
   */
  static async getIPSecurityOverview(req: Request, res: Response): Promise<void> {
    try {
      const securityService = new SecurityService();
      const overview = await securityService.getIPSecurityOverview();

      const response: ApiResponse<any> = {
        success: true,
        data: overview
      };

      res.json(response);
    } catch (error) {
      console.error('获取IP风控概览失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取IP风控概览失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 获取IP风控日志列表
   */
  static async getIPRiskLogs(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = '1',
        limit = '20',
        ipAddress,
        actionType,
        isBlocked,
        startDate,
        endDate
      } = req.query;

      const securityService = new SecurityService();
      const options: any = {
        page: parseInt(page as string),
        limit: parseInt(limit as string)
      };

      if (ipAddress) options.ipAddress = ipAddress as string;
      if (actionType) options.actionType = actionType as string;
      if (isBlocked === 'true') options.isBlocked = true;
      if (isBlocked === 'false') options.isBlocked = false;
      if (startDate) options.startDate = new Date(startDate as string);
      if (endDate) options.endDate = new Date(endDate as string);

      const result = await securityService.getIPRiskLogs(options);

      const response: ApiResponse<any> = {
        success: true,
        data: result
      };

      res.json(response);
    } catch (error) {
      console.error('获取IP风控日志失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取IP风控日志失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 获取IP风控规则列表
   */
  static async getIPRiskRules(req: Request, res: Response): Promise<void> {
    try {
      const securityService = new SecurityService();
      const rules = await securityService.getIPRiskRules();

      const response: ApiResponse<any> = {
        success: true,
        data: rules
      };

      res.json(response);
    } catch (error) {
      console.error('获取IP风控规则失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取IP风控规则失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 创建IP风控规则
   */
  static async createIPRiskRule(req: Request, res: Response): Promise<void> {
    try {
      const { ruleName, ruleType, timeWindow, maxAttempts, blockDuration, isActive } = req.body;

      // 验证必填字段
      if (!ruleName || !ruleType || !timeWindow || !maxAttempts || !blockDuration) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '缺少必填字段',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const securityService = new SecurityService();
      const rule = await securityService.createIPRiskRule({
        ruleName,
        ruleType,
        timeWindow: parseInt(timeWindow),
        maxAttempts: parseInt(maxAttempts),
        blockDuration: parseInt(blockDuration),
        isActive
      });

      // 记录操作日志
      await LogService.logAdminOperation(
        Number(req.user?.id) || 0,
        'create_ip_risk_rule',
        'security',
        rule.id,
        { ruleName, ruleType },
        req.ip || 'unknown'
      );

      const response: ApiResponse<any> = {
        success: true,
        data: rule
      };

      res.json(response);
    } catch (error) {
      console.error('创建IP风控规则失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '创建IP风控规则失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 更新IP风控规则
   */
  static async updateIPRiskRule(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      if (!id) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '缺少规则ID',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const securityService = new SecurityService();
      const rule = await securityService.updateIPRiskRule(parseInt(id), updateData);

      // 记录操作日志
      await LogService.logAdminOperation(
        Number(req.user?.id) || 0,
        'update_ip_risk_rule',
        'security',
        rule.id,
        updateData,
        req.ip || 'unknown'
      );

      const response: ApiResponse<any> = {
        success: true,
        data: rule
      };

      res.json(response);
    } catch (error) {
      console.error('更新IP风控规则失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '更新IP风控规则失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 删除IP风控规则
   */
  static async deleteIPRiskRule(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '缺少规则ID',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const securityService = new SecurityService();
      await securityService.deleteIPRiskRule(parseInt(id));

      // 记录操作日志
      await LogService.logAdminOperation(
        Number(req.user?.id) || 0,
        'delete_ip_risk_rule',
        'security',
        parseInt(id),
        { ruleId: id },
        req.ip || 'unknown'
      );

      const response: ApiResponse<any> = {
        success: true,
        data: { message: '风控规则删除成功' }
      };

      res.json(response);
    } catch (error) {
      console.error('删除IP风控规则失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '删除IP风控规则失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 手动封禁IP
   */
  static async blockIP(req: Request, res: Response): Promise<void> {
    try {
      const { ipAddress, blockDuration, reason } = req.body;

      // 验证必填字段
      if (!ipAddress || !blockDuration || !reason) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '缺少必填字段',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const securityService = new SecurityService();
      const blockRecord = await securityService.blockIP({
        ipAddress,
        blockDuration: parseInt(blockDuration),
        reason,
        adminId: Number(req.user?.id) || 0
      });

      // 记录操作日志
      await LogService.logAdminOperation(
        Number(req.user?.id) || 0,
        'manual_block_ip',
        'security',
        blockRecord.id,
        { ipAddress, blockDuration, reason },
        req.ip || 'unknown'
      );

      const response: ApiResponse<any> = {
        success: true,
        data: blockRecord
      };

      res.json(response);
    } catch (error) {
      console.error('封禁IP失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '封禁IP失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 解封IP
   */
  static async unblockIP(req: Request, res: Response): Promise<void> {
    try {
      const { ipAddress } = req.body;

      if (!ipAddress) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '缺少IP地址',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const securityService = new SecurityService();
      const unblockRecord = await securityService.unblockIP(
        ipAddress,
        Number(req.user?.id) || 0
      );

      // 记录操作日志
      await LogService.logAdminOperation(
        Number(req.user?.id) || 0,
        'manual_unblock_ip',
        'security',
        unblockRecord.id,
        { ipAddress },
        req.ip || 'unknown'
      );

      const response: ApiResponse<any> = {
        success: true,
        data: unblockRecord
      };

      res.json(response);
    } catch (error) {
      console.error('解封IP失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '解封IP失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 获取用户权限概览
   */
  static async getUserPermissionsOverview(req: Request, res: Response): Promise<void> {
    try {
      const securityService = new SecurityService();
      const overview = await securityService.getUserPermissionsOverview();

      const response: ApiResponse<any> = {
        success: true,
        data: overview
      };

      res.json(response);
    } catch (error) {
      console.error('获取用户权限概览失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户权限概览失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 获取用户会话列表
   */
  static async getUserSessions(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = '1',
        limit = '20',
        userId,
        ipAddress,
        isActive
      } = req.query;

      const securityService = new SecurityService();
      const options: any = {
        page: parseInt(page as string),
        limit: parseInt(limit as string)
      };

      if (userId) options.userId = parseInt(userId as string);
      if (ipAddress) options.ipAddress = ipAddress as string;
      if (isActive === 'true') options.isActive = true;
      if (isActive === 'false') options.isActive = false;

      const result = await securityService.getUserSessions(options);

      const response: ApiResponse<any> = {
        success: true,
        data: result
      };

      res.json(response);
    } catch (error) {
      console.error('获取用户会话列表失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取用户会话列表失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 强制注销用户会话
   */
  static async revokeUserSession(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '缺少会话ID',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const securityService = new SecurityService();
      const session = await securityService.revokeUserSession(
        parseInt(sessionId),
        Number(req.user?.id) || 0
      );

      const response: ApiResponse<any> = {
        success: true,
        data: session
      };

      res.json(response);
    } catch (error) {
      console.error('注销用户会话失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '注销用户会话失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 获取安全配置
   */
  static async getSecurityConfigs(req: Request, res: Response): Promise<void> {
    try {
      const securityService = new SecurityService();
      const configs = await securityService.getSecurityConfigs();

      const response: ApiResponse<any> = {
        success: true,
        data: configs
      };

      res.json(response);
    } catch (error) {
      console.error('获取安全配置失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '获取安全配置失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }

  /**
   * 更新安全配置
   */
  static async updateSecurityConfig(req: Request, res: Response): Promise<void> {
    try {
      const { configKey, configValue } = req.body;

      if (!configKey || configValue === undefined) {
        res.status(400).json({
          success: false,
          error: {
            code: ErrorCodes.VALIDATION_ERROR,
            message: '缺少配置键或配置值',
            timestamp: new Date().toISOString()
          }
        } as ApiResponse);
        return;
      }

      const securityService = new SecurityService();
      const config = await securityService.updateSecurityConfig(
        configKey,
        configValue,
        Number(req.user?.id) || 0
      );

      const response: ApiResponse<any> = {
        success: true,
        data: config
      };

      res.json(response);
    } catch (error) {
      console.error('更新安全配置失败:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ErrorCodes.INTERNAL_ERROR,
          message: '更新安全配置失败',
          timestamp: new Date().toISOString()
        }
      } as ApiResponse);
    }
  }
}
