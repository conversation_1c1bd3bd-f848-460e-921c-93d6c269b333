{"version": 3, "file": "init-level-configs.js", "sourceRoot": "", "sources": ["../../src/scripts/init-level-configs.ts"], "names": [], "mappings": ";;;AAgHS,4CAAgB;AA1GzB,iDAA4C;AAE5C,KAAK,UAAU,gBAAgB;IAC7B,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAGjC,MAAM,iBAAM,CAAC,QAAQ,EAAE,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAGzB,MAAM,YAAY,GAAG;YACnB;gBACE,KAAK,EAAE,MAAM;gBACb,WAAW,EAAE,MAAM;gBACnB,eAAe,EAAE,EAAE;gBACnB,WAAW,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;gBACpC,eAAe,EAAE,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;gBAC1C,iBAAiB,EAAE,KAAK;gBACxB,eAAe,EAAE,KAAK;gBACtB,YAAY,EAAE,KAAK;gBACnB,oBAAoB,EAAE,CAAC;aACxB;YACD;gBACE,KAAK,EAAE,MAAM;gBACb,WAAW,EAAE,QAAQ;gBACrB,eAAe,EAAE,EAAE;gBACnB,WAAW,EAAE,MAAM,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;gBACrC,eAAe,EAAE,MAAM,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;gBAC3C,iBAAiB,EAAE,IAAI;gBACvB,eAAe,EAAE,KAAK;gBACtB,YAAY,EAAE,KAAK;gBACnB,oBAAoB,EAAE,CAAC;aACxB;YACD;gBACE,KAAK,EAAE,MAAM;gBACb,WAAW,EAAE,QAAQ;gBACrB,eAAe,EAAE,GAAG;gBACpB,WAAW,EAAE,MAAM,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;gBACrC,eAAe,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;gBAC/C,iBAAiB,EAAE,IAAI;gBACvB,eAAe,EAAE,IAAI;gBACrB,YAAY,EAAE,KAAK;gBACnB,oBAAoB,EAAE,CAAC;aACxB;YACD;gBACE,KAAK,EAAE,MAAM;gBACb,WAAW,EAAE,QAAQ;gBACrB,eAAe,EAAE,IAAI;gBACrB,WAAW,EAAE,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC;gBACtC,eAAe,EAAE,MAAM,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;gBAChD,iBAAiB,EAAE,IAAI;gBACvB,eAAe,EAAE,IAAI;gBACrB,YAAY,EAAE,IAAI;gBAClB,oBAAoB,EAAE,EAAE;aACzB;SACF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE,CAAC;YAClC,MAAM,iBAAM,CAAC,eAAe,CAAC,MAAM,CAAC;gBAClC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE;gBAC9B,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,kBAAkB,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;QACtD,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,iBAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;YACvD,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;SAC1B,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;YAC3D,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;YACtD,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5E,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAChF,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC;YAC3D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IAEjC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;QACvC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;YAAS,CAAC;QACT,MAAM,iBAAM,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,gBAAgB,EAAE;SACf,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC/B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}