import React from 'react';
import {
  AreaChart as RechartsAreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';

interface DataPoint {
  [key: string]: any;
}

interface AreaChartProps {
  data: DataPoint[];
  dataKey: string;
  xAxisKey: string;
  title?: string;
  color?: string;
  height?: number;
  showGrid?: boolean;
  showTooltip?: boolean;
  formatXAxis?: (value: any) => string;
  formatTooltip?: (value: any, name: string) => [string, string];
  className?: string;
}

const CustomTooltip: React.FC<any> = ({ active, payload, label, formatTooltip }) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    const [formattedValue, formattedName] = formatTooltip 
      ? formatTooltip(data.value, data.name)
      : [data.value, data.name];

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm text-gray-600 mb-1">{label}</p>
        <p className="text-sm font-medium" style={{ color: data.color }}>
          {formattedName}: {formattedValue}
        </p>
      </div>
    );
  }
  return null;
};

export function AreaChart({
  data,
  dataKey,
  xAxisKey,
  title,
  color = '#3b82f6',
  height = 300,
  showGrid = true,
  showTooltip = true,
  formatXAxis,
  formatTooltip,
  className = ''
}: AreaChartProps) {
  return (
    <div className={`w-full ${className}`}>
      {title && (
        <h3 className="text-sm font-medium text-gray-900 mb-4">{title}</h3>
      )}
      <div style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <RechartsAreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
            <XAxis
              dataKey={xAxisKey}
              tickFormatter={formatXAxis}
              fontSize={12}
              stroke="#6b7280"
              axisLine={false}
              tickLine={false}
            />
            <YAxis
              fontSize={12}
              stroke="#6b7280"
              axisLine={false}
              tickLine={false}
            />
            {showTooltip && (
              <Tooltip
                content={<CustomTooltip formatTooltip={formatTooltip} />}
              />
            )}
            <Area
              type="monotone"
              dataKey={dataKey}
              stroke={color}
              fill={color}
              fillOpacity={0.3}
              strokeWidth={2}
            />
          </RechartsAreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
